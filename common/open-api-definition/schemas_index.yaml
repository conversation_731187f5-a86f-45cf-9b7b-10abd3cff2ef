escoOccupationDetail:
  $ref: "./classifications/esco/schemas/EscoOccupationDetail.yaml"
escoOccupationSummary:
  $ref: "./classifications/esco/schemas/EscoOccupationSummary.yaml"
escoOccupationPage:
  $ref: "./classifications/esco/schemas/EscoOccupationPage.yaml"
escoOccupationWithIscoOccaptionSummary:
  $ref: "./classifications/esco/schemas/EscoOccupationWithIscoOccaptionSummary.yaml"
skill:
  $ref: "./classifications/esco/schemas/Skill.yaml"
skillSummary:
  $ref: "./classifications/esco/schemas/SkillSummary.yaml"
skillLinkToActivityCommand:
  $ref: "./classifications/esco/schemas/SkillLinkToActivityCommand.yaml"
skillLinkToBehaviorCommand:
  $ref: "./classifications/esco/schemas/SkillLinkToBehaviorCommand.yaml"
skillLinkToContextCommand:
  $ref: "./classifications/esco/schemas/SkillLinkToContextCommand.yaml"
skillSetBooleanValueCommand:
  $ref: "./classifications/esco/schemas/SkillSetBooleanValueCommand.yaml"

erhgoOccupationsForLabel:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationsForLabel.yaml"
erhgoOccupationMinimumInfo:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationMinimumInfo.yaml"
erhgoOccupationSearch:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationSearch.yaml"
erhgoOccupationDetail:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationDetail.yaml"
erhgoOccupationSumUp:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationSumUp.yaml"
erhgoOccupationSummary:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationSummary.yaml"
erhgoOccupationOTSummary:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationOTSummary.yaml"
erhgoOccupationPage:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationPage.yaml"
erhgoOccupationOTPage:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationOTPage.yaml"
masteryLevel:
  $ref: "./classifications/erhgo-occupation/schemas/MasteryLevel.yaml"
erhgoOccupationState:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationState.yaml"
erhgoSearchOrder:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoSearchOrder.yaml"
updateMasteryLevelCommand:
  $ref: "./classifications/erhgo-occupation/schemas/UpdateMasteryLevelCommand.yaml"
updateDescriptionCommand:
  $ref: "./classifications/erhgo-occupation/schemas/UpdateDescriptionCommand.yaml"
updateBehaviorsDescriptionCommand:
  $ref: "./classifications/erhgo-occupation/schemas/UpdateBehaviorsDescriptionCommand.yaml"
linkToErhgoOccupationCommand:
  $ref: "./classifications/erhgo-occupation/schemas/LinkRomeToErhgoOccupationCommand.yaml"
unlinkRomeFromErhgoOccupationCommand:
  $ref: "./classifications/erhgo-occupation/schemas/UnlinkRomeFromErhgoOccupationCommand.yaml"
mandatoryState:
  $ref: "./classifications/erhgo-occupation/schemas/MandatoryState.yaml"
occupationReferentialEntityEditCommand:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationReferentialEntityEditCommand.yaml"
occupationReferentialEntitiesEditCommand:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationReferentialEntitiesEditCommand.yaml"
occupationReferentialEntityEditWithStateCommand:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationReferentialEntityEditWithStateCommand.yaml"
abstractOccupationEntity:
  $ref: "./classifications/erhgo-occupation/schemas/AbstractOccupationEntity.yaml"
abstractOccupationEntityWithState:
  $ref: "./classifications/erhgo-occupation/schemas/AbstractOccupationEntityWithState.yaml"
occupationActivity:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationActivity.yaml"
occupationContext:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationContext.yaml"
occupationBehavior:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationBehavior.yaml"
occupationQualificationSource:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationQualificationSource.yaml"
editAlternativeLabelsCommand:
  $ref: "./classifications/erhgo-occupation/schemas/EditAlternativeLabelsCommand.yaml"
erhgoOccupationBehaviorsCategories:
  $ref: "./classifications/erhgo-occupation/schemas/ErhgoOccupationBehaviorsCategories.yaml"
editOccupationBehaviorCategoryCommand:
  $ref: "./classifications/erhgo-occupation/schemas/EditOccupationBehaviorCategoryCommand.yaml"
editEscoOccupationCommand:
  $ref: "./classifications/erhgo-occupation/schemas/EditEscoOccupationCommand.yaml"
createErhgoOccupationCommand:
  $ref: "./classifications/erhgo-occupation/schemas/CreateErhgoOccupationCommand.yaml"
mergeOccupationsCommand:
  $ref: "./classifications/erhgo-occupation/schemas/MergeOccupationsCommand.yaml"
updateErhgoClassificationsCommand:
  $ref: "./classifications/erhgo-occupation/schemas/UpdateErhgoClassificationsCommand.yaml"
generatedOccupationDescription:
  $ref: "./classifications/erhgo-occupation/schemas/GeneratedOccupationDescription.yaml"
generationReportItem:
  $ref: "./classifications/erhgo-occupation/schemas/GenerationReportItem.yaml"
generateSimilarLabelOccupationCommand:
  $ref: "./classifications/erhgo-occupation/schemas/GenerateSimilarLabelOccupationCommand.yaml"
occupationCreationReason:
  $ref: "./classifications/erhgo-occupation/schemas/OccupationCreationReason.yaml"

scrapeOffersCommand:
  $ref: "./external-offer/schemas/ScrapeOffersCommand.yaml"


genericOpenAiPromptTesterCommand:
  $ref: "./generation/schemas/GenericOpenAiPromptTesterCommand.yaml"
chatCompletionResponse:
  $ref: "./generation/schemas/ChatCompletionResponse.yaml"


iscoSummary:
  $ref: "./classifications/isco/schemas/IscoSummary.yaml"


monthlyCandidatures:
  $ref: "./statistics/candidate/schemas/MonthlyCandidatures.yaml"
MonthlyCandidaturesStats:
  $ref: "./statistics/candidate/schemas/MonthlyCandidaturesStats.yaml"
MonthlySpontaneousCandidaturesStats:
  $ref: "./statistics/candidate/schemas/MonthlySpontaneousCandidaturesStats.yaml"

recruitmentStats:
  $ref: "./statistics/recruitment/schemas/RecruitmentStats.yaml"

severalMetricsStats:
  $ref: "./statistics/several-metrics/schemas/SeveralMetricsStats.yaml"




romeSummary:
  $ref: "./classifications/rome/schemas/RomeSummary.yaml"
romePage:
  $ref: "./classifications/rome/schemas/RomePage.yaml"


contextCategory:
  $ref: "./referential/context/schemas/ContextCategory.yaml"
contextCategoryLevel:
  $ref: "./referential/context/schemas/ContextCategoryLevel.yaml"
contextSummary:
  $ref: "./referential/context/schemas/ContextSummary.yaml"
saveContextCommand:
  $ref: "./referential/context/schemas/SaveContextCommand.yaml"
referentialElementOrigin:
  $ref: "./referential/context/schemas/ReferentialElementOrigin.yaml"
context:
  $ref: "./referential/context/schemas/Context.yaml"
contextsPage:
  $ref: "./referential/context/schemas/ContextsPage.yaml"
contextsForCategory:
  $ref: "./referential/context/schemas/ContextsForCategory.yaml"
contextsForCategoryDetail:
  $ref: "./referential/context/schemas/ContextsForCategoryDetail.yaml"
saveQuestionForContextsCommand:
  $ref: "./referential/context/schemas/SaveQuestionForContextsCommand.yaml"
questionForContextsSummary:
  $ref: "./referential/context/schemas/QuestionForContextsSummary.yaml"
questionForContextsDetails:
  $ref: "./referential/context/schemas/QuestionForContextsDetails.yaml"
questionForContextsPage:
  $ref: "./referential/context/schemas/QuestionForContextsPage.yaml"
suggestedAnswers:
  $ref: "./referential/context/schemas/SuggestedAnswers.yaml"


saveCapacityRelatedQuestionCommand:
  $ref: "./referential/capacity-related-question/schemas/SaveQuestionCommand.yaml"
saveAnswerToCapacityRelatedQuestionCommand:
  $ref: "./user/capacity-related-question/schemas/SaveAnswerCommand.yaml"
responseForCapacityRelatedQuestion:
  $ref: "./referential/capacity-related-question/schemas/Response.yaml"
capacityRelatedQuestionDetails:
  $ref: "./referential/capacity-related-question/schemas/QuestionDetails.yaml"
capacityRelatedQuestionSummary:
  $ref: "./referential/capacity-related-question/schemas/QuestionSummary.yaml"
capacityRelatedQuestionSummaryForUser:
  $ref: "./user/capacity-related-question/schemas/QuestionSummaryForUser.yaml"
optionalMenu:
  $ref: "./user/user/schemas/OptionalMenu.yaml"
capacityRelatedQuestionPage:
  $ref: "./referential/capacity-related-question/schemas/QuestionPage.yaml"
questionType:
  $ref: "./referential/capacity-related-question/schemas/QuestionType.yaml"


activityLabelSummary:
  $ref: "./referential/activity/schemas/ActivityLabelSummary.yaml"
acquisitionModality:
  $ref: "./referential/activity/schemas/AcquisitionModality.yaml"
activityLabelWithCapacities:
  $ref: "./referential/activity/schemas/ActivityLabelWithCapacities.yaml"
frequency:
  $ref: "./referential/activity/schemas/Frequency.yaml"
activityLabel:
  $ref: "./referential/activity/schemas/ActivityLabel.yaml"
activity:
  $ref: "./referential/activity/schemas/Activity.yaml"
activityType:
  $ref: "./referential/activity/schemas/ActivityType.yaml"
saveActivityCommand:
  $ref: "./referential/activity/schemas/SaveActivityCommand.yaml"
activityLabelPage:
  $ref: "./referential/activity/schemas/ActivityLabelPage.yaml"
activityWithLevel:
  $ref: "./referential/activity/schemas/ActivityWithLevel.yaml"


capacityDetail:
  $ref: "./referential/capacity/schemas/CapacityDetail.yaml"
capacity:
  $ref: "./referential/capacity/schemas/Capacity.yaml"
capacitiesResult:
  $ref: "./referential/capacity/schemas/CapacitiesResult.yaml"


behavior:
  $ref: "./referential/behavior/schemas/Behavior.yaml"
behaviorPage:
  $ref: "./referential/behavior/schemas/BehaviorPage.yaml"
behaviorCategory:
  $ref: "./referential/behavior/schemas/BehaviorCategory.yaml"


createMissionCommand:
  $ref: "./job/mission/schemas/CreateMissionCommand.yaml"
updateMissionCommand:
  $ref: "./job/mission/schemas/UpdateMissionCommand.yaml"
mission:
  $ref: "./job/mission/schemas/Mission.yaml"
missionDetail:
  $ref: "./job/mission/schemas/MissionDetail.yaml"


matchingType:
  $ref: "./matching/schemas/MatchingType.yaml"
effort:
  $ref: "./matching/schemas/Effort.yaml"


jobDetail:
  $ref: "./job/job/schemas/JobDetail.yaml"
jobEvaluationState:
  $ref: "./job/job/schemas/JobEvaluationState.yaml"
jobType:
  $ref: "./job/job/schemas/JobType.yaml"
jobPage:
  $ref: "./job/job/schemas/JobPage.yaml"
jobSummary:
  $ref: "./job/job/schemas/JobSummary.yaml"
jobSummaryWithRecruitmentProfile:
  $ref: "./job/job/schemas/JobSummaryWithRecruitmentProfile.yaml"
saveJobCommand:
  $ref: "./job/job/schemas/SaveJobCommand.yaml"
workingTime:
  $ref: "./job/job/schemas/WorkingTime.yaml"
typeContractCategory:
  $ref: "./job/job/schemas/TypeContractCategory.yaml"


recruitmentState:
  $ref: "./recruitment/schemas/RecruitmentState.yaml"
processingType:
  $ref: "./recruitment/schemas/ProcessingType.yaml"
recruitmentProfileSummary:
  $ref: "./recruitment/schemas/RecruitmentProfileSummary.yaml"
recruitment:
  $ref: "./recruitment/schemas/Recruitment.yaml"
recruitmentDetail:
  $ref: "./recruitment/schemas/RecruitmentDetail.yaml"
typeContract:
  $ref: "./recruitment/schemas/TypeContract.yaml"
workContractDurationUnit:
  $ref: "./recruitment/schemas/WorkContractDurationUnit.yaml"
saveRecruitmentCommand:
  $ref: "./recruitment/schemas/SaveRecruitmentCommand.yaml"
recruitmentPage:
  $ref: "./recruitment/schemas/RecruitmentPage.yaml"
recruitmentSummary:
  $ref: "./recruitment/schemas/RecruitmentSummary.yaml"
recruitmentPageForCandidate:
  $ref: "./recruitment/schemas/RecruitmentPageForCandidate.yaml"
recruitmentSummaryForCandidate:
  $ref: "./recruitment/schemas/RecruitmentSummaryForCandidate.yaml"
recruitmentSort:
  $ref: "./recruitment/schemas/RecruitmentSort.yaml"
candidatureCountItem:
  $ref: "./recruitment/schemas/CandidatureCountItem.yaml"


generalInformation:
  $ref: "./user/user/schemas/GeneralInformation.yaml"
MatchingUserSummary:
  $ref: "./user/user/schemas/MatchingUserSummary.yaml"
UserMatchingJobPage:
  $ref: "./user/user/schemas/UserMatchingJobPage.yaml"
SimpleRecruitmentPage:
  $ref: "./user/user/schemas/RecruitmentPage.yaml"
SimpleRecruitmentSummary:
  $ref: "./user/user/schemas/RecruitmentSummary.yaml"
SimpleRecruitmentCount:
  $ref: "./user/user/schemas/RecruitmentCount.yaml"
UserContactInfo:
  $ref: "./user/user/schemas/UserContactInfo.yaml"
UserByGroupPage:
  $ref: "./user/user/schemas/UserByGroupPage.yaml"
contactTime:
  $ref: "./user/user/schemas/ContactTime.yaml"
contactForCandidature:
  $ref: "./user/user/schemas/ContactForCandidature.yaml"
userProfileDetailWithCapacities:
  $ref: "./user/user/schemas/UserProfileDetailWithCapacities.yaml"
userCandidatureState:
  $ref: "./user/user/schemas/UserCandidatureState.yaml"
candidatureState:
  $ref: "./candidature/job/schemas/GlobalCandidatureState.yaml"
availabilityForCandidature:
  $ref: "./candidature/job/schemas/AvailabilityForCandidature.yaml"
userProfileSummary:
  $ref: "./user/user/schemas/UserProfileSummary.yaml"
userProfileProgress:
  $ref: "./user/user/schemas/UserProfileProgress.yaml"
userSummary:
  $ref: "./user/user/schemas/UserSummary.yaml"
foUserSummary:
  $ref: "./user/user/schemas/FOUserSummary.yaml"
userPage:
  $ref: "./user/user/schemas/UserPage.yaml"
initializeProfileCommand:
  $ref: "./user/user/schemas/InitializeProfileCommand.yaml"
userChannelSource:
  $ref: "./user/user/schemas/UserChannelSource.yaml"
saveUserContactInfoCommand:
  $ref: "./user/user/schemas/SaveUserContactInfoCommand.yaml"
saveUserNameCommand:
  $ref: "./user/user/schemas/SaveUserNameCommand.yaml"
SetFrontOfficeUserPasswordCommand:
  $ref: "./user/user/schemas/SetFrontOfficeUserPasswordCommand.yaml"
ConfirmFOUserFromBOCommand:
  $ref: "./user/user/schemas/ConfirmFOUserFromBOCommand.yaml"
situation:
  $ref: "./user/user/schemas/Situation.yaml"
userCandidature:
  $ref: "./user/user/schemas/UserJobCandidature.yaml"
updateUsersChannelsCommand:
  $ref: "./user/user/schemas/UpdateUsersChannelsCommand.yaml"
userRegistrationStateStep:
  $ref: "./user/user/schemas/UserRegistrationStateStep.yaml"
userRegistrationState:
  $ref: "./user/user/schemas/UserRegistrationState.yaml"
location:
  $ref: "./misc/schemas/Location.yaml"
userNote:
  $ref: "./user/user/schemas/UserNote.yaml"
userNotification:
  $ref: "./user/user/schemas/UserNotification.yaml"
userNotificationState:
  $ref: "./user/user/schemas/UserNotificationState.yaml"
userNotificationEntityType:
  $ref: "./user/user/schemas/UserNotificationEntityType.yaml"
mailingListOptIn:
  $ref: "./user/user/schemas/MailingListOptIn.yaml"
updateUserMailingListOptInCommand:
  $ref: "./user/user/schemas/UpdateUserMailingListOptInCommand.yaml"
userBehaviorDescription:
  $ref: "./user/user/schemas/UserBehaviorDescription.yaml"
userFileImportState:
  $ref: "./user/user/schemas/FileImportState.yaml"
userProfileCompletion:
  $ref: "./user/user/schemas/UserProfileCompletion.yaml"
hashtags:
  $ref: "./user/user/schemas/Hashtags.yaml"
userHandicapInfo:
  $ref: "./user/user/schemas/UserHandicapInfo.yaml"


usersMobileNotification:
  $ref: "./user/mobile/schemas/UsersMobileNotification.yaml"


algoliaQuery:
  $ref: "./user/notification/schemas/AlgoliaQuery.yaml"


candidature:
  $ref: "./candidature/common/schemas/Candidature.yaml"
simpleCandidature:
  $ref: "./candidature/common/schemas/SimpleCandidature.yaml"
candidaturePreview:
  $ref: "./candidature/job/schemas/CandidaturePreview.yaml"
customQuestionAndAnswer:
  $ref: "./candidature/job/schemas/CustomQuestionAndAnswer.yaml"
candidatureNote:
  $ref: "./candidature/job/schemas/CandidatureNote.yaml"
candidatureSummary:
  $ref: "./candidature/job/schemas/CandidatureSummary.yaml"
candidatureContactInfoPage:
  $ref: "./candidature/job/schemas/CandidatureContactInfoPage.yaml"
contextPositioning:
  $ref: "./candidature/job/schemas/ContextPositioning.yaml"
contextToEvaluateReferencingExperiences:
  $ref: "./candidature/job/schemas/ContextToEvaluateReferencingExperiences.yaml"
contextToEvaluate:
  $ref: "./candidature/job/schemas/ContextToEvaluate.yaml"
generateCandidaturesOnRecruitmentsCommand:
  $ref: "./candidature/job/schemas/GenerateCandidaturesOnRecruitmentsCommand.yaml"
candidatureInitializationData:
  $ref: "./candidature/job/schemas/CandidatureInitializationData.yaml"


experienceType:
  $ref: "./user/experience/schemas/ExperienceType.yaml"
experienceDetails:
  $ref: "./user/experience/schemas/ExperienceDetails.yaml"
experienceDetailsWithCapacities:
  $ref: "./user/experience/schemas/ExperienceDetailsWithCapacities.yaml"
experienceSummary:
  $ref: "./user/experience/schemas/ExperienceSummary.yaml"


abstractItemPage:
  $ref: "./misc/schemas/AbstractItemPage.yaml"
sortDirection:
  $ref: "./misc/schemas/SortDirection.yaml"
createdBy:
  $ref: "./misc/schemas/CreatedBy.yaml"
auditing:
  $ref: "./misc/schemas/Auditing.yaml"
customEmailTemplate:
  $ref: "./misc/schemas/CustomEmailTemplate.yaml"


algoliaConfig:
  $ref: "./config/schemas/AlgoliaSearchConfiguration.yaml"


saveLandingPageCommand:
  $ref: "./landing-page/schemas/SaveLandingPageCommand.yaml"
landingPageSummary:
  $ref: "./landing-page/schemas/LandingPageSummary.yaml"
landingPageDetail:
  $ref: "./landing-page/schemas/LandingPageDetail.yaml"
landingPagePage:
  $ref: "./landing-page/schemas/LandingPagePage.yaml"


saveOrganizationCommand:
  $ref: "./organization/schemas/SaveOrganizationCommand.yaml"
organizationType:
  $ref: "./organization/schemas/OrganizationType.yaml"
diffusionType:
  $ref: "./organization/schemas/DiffusionType.yaml"
organization:
  $ref: "./organization/schemas/Organization.yaml"
organizationSummary:
  $ref: "./organization/schemas/OrganizationSummary.yaml"
organizationPage:
  $ref: "./organization/schemas/OrganizationPage.yaml"

criteria:
  $ref: "./criteria/schemas/Criteria.yaml"
userCriteriaValue:
  $ref: "./criteria/schemas/UserCriteriaValue.yaml"
criteriaValue:
  $ref: "./criteria/schemas/CriteriaValue.yaml"
simpleCriteria:
  $ref: "./criteria/schemas/SimpleCriteria.yaml"
simpleUserCriteriaValue:
  $ref: "./criteria/schemas/SimpleUserCriteriaValue.yaml"
criteriaQuestionType:
  $ref: "./criteria/schemas/QuestionType.yaml"
saveUserCriteriasCommand:
  $ref: "./user/user/schemas/SaveUserCriteriaCommand.yaml"
editCriteriaCommand:
  $ref: "./criteria/schemas/EditCriteriaCommand.yaml"
sourcingCriteriaStep:
  $ref: "./criteria/schemas/SourcingCriteriaStep.yaml"
sourcingCandidaturePage:
  $ref: "./sourcing/schemas/SourcingCandidaturePage.yaml"
sourcingCandidatureItem:
  $ref: "./sourcing/schemas/SourcingCandidatureItem.yaml"
sourcingCandidatureDetail:
  $ref: "./sourcing/schemas/SourcingCandidatureDetail.yaml"
sourcingCandidatureState:
  $ref: "./sourcing/schemas/SourcingCandidatureState.yaml"
sourcingCandidatureSort:
  $ref: "./sourcing/schemas/SourcingCandidatureSort.yaml"
sourcingOrganization:
  $ref: "./sourcing/schemas/SourcingOrganization.yaml"
sourcingRecruitmentItem:
  $ref: "./sourcing/schemas/SourcingRecruitmentItem.yaml"
sourcingJobAndRecruitment:
  $ref: "./sourcing/schemas/SourcingJobAndRecruitment.yaml"
createSourcingJobAndRecruitmentCommand:
  $ref: "./sourcing/schemas/CreateOrUpdateSourcingJobAndRecruitmentCommand.yaml"
SourcingInvitationCode:
  $ref: "./sourcing/schemas/SourcingInvitationCode.yaml"
SaveSourcingInvitationCode:
  $ref: "./sourcing/schemas/SaveInvitationCodeCommand.yaml"
CandidateDetail:
  $ref: "./sourcing/schemas/CandidateDetail.yaml"
sourcingSubscription:
  $ref: "./sourcing/schemas/SourcingSubscription.yaml"
sourcingSubscriptionType:
  $ref: "./sourcing/schemas/SourcingSubscriptionType.yaml"
SourcingUser:
  $ref: "./sourcing/schemas/SourcingUser.yaml"
SourcingUserDetails:
  $ref: "./sourcing/schemas/SourcingUserDetails.yaml"
SourcingPreferences:
  $ref: "./sourcing/schemas/SourcingPreferences.yaml"
CommonSourcingRecruitmentInfos:
  $ref: "./sourcing/schemas/CommonSourcingRecruitmentInfos.yaml"
UsersToNotifySelectionType:
  $ref: "./sourcing/schemas/UsersToNotifySelectionType.yaml"


workEnvironment:
  $ref: "./referential/work-environment/schemas/WorkEnvironment.yaml"


configurableProperty:
  $ref: "./config/schemas/ConfigurableProperty.yaml"


erhgoClassification:
  $ref: "./classifications/erhgo/schemas/ErhgoClassification.yaml"


notificationType:
  $ref: "./recruitment/schemas/NotificationType.yaml"


incompleteInformation:
  $ref: "./user/user/schemas/IncompleteInformation.yaml"



sector:
  $ref: "./sector/schemas/Sector.yaml"


externalOfferSummary:
  $ref: "./external-offer/schemas/ExternalOfferSummary.yaml"
recruitmentCreationState:
  $ref: "./external-offer/schemas/RecruitmentCreationState.yaml"
externalOffersPage:
  $ref: "./external-offer/schemas/ExternalOffersPage.yaml"
