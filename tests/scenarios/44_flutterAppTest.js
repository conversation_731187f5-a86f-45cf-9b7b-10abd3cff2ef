module.exports = {
  '@tags': ['test', 'flutter', 'external'],

  /**
   * @param {NightwatchAPI} client
   */
  'Flutter app navigation test': function(client) {
    const stagingUrl = 'https://staging.jenesuispasuncv.fr/welcome';
    
    client
      // Navigate to the staging environment
      .url(stagingUrl)
      .pause(2000) // Initial page load wait
      
      // Wait for Flutter application to fully load
      .waitForElementPresent('body', 30000)
      
      // Wait for Flutter canvas/flt-glass-pane elements to be ready
      .perform(function() {
        console.log('Waiting for Flutter app initialization...');
      })
      
      // Wait for Flutter CanvasKit initialization - check for common Flutter elements
      .execute(function() {
        // Wait for Flutter app to be ready by checking for flt-glass-pane or canvas elements
        var checkFlutterReady = function() {
          var flutterElements = document.querySelectorAll('flt-glass-pane, canvas, [flt-renderer]');
          var flutterScripts = document.querySelectorAll('script[src*="flutter"], script[src*="canvaskit"]');
          return flutterElements.length > 0 || flutterScripts.length > 0 || window.flutterCanvasKit !== undefined;
        };
        
        return new Promise(function(resolve) {
          var attempts = 0;
          var maxAttempts = 60; // 60 seconds max wait
          
          var checkInterval = setInterval(function() {
            attempts++;
            if (checkFlutterReady() || attempts >= maxAttempts) {
              clearInterval(checkInterval);
              resolve({
                ready: checkFlutterReady(),
                attempts: attempts,
                elements: document.querySelectorAll('flt-glass-pane, canvas, [flt-renderer]').length
              });
            }
          }, 1000);
        });
      }, [], function(result) {
        console.log('Flutter app readiness check:', result.value);
      })
      
      // Additional wait for network idle state simulation
      .pause(5000)
      
      // Store initial URL for comparison
      .url(function(result) {
        this.initialUrl = result.value;
        console.log('Initial URL:', result.value);
      })
      
      // Perform coordinate-based clicking at viewport center-bottom position (x: 50%, y: 80%)
      .execute(function() {
        // Calculate coordinates based on viewport size
        var viewportWidth = window.innerWidth || document.documentElement.clientWidth;
        var viewportHeight = window.innerHeight || document.documentElement.clientHeight;
        
        var clickX = Math.floor(viewportWidth * 0.5);  // 50% of viewport width
        var clickY = Math.floor(viewportHeight * 0.8); // 80% of viewport height
        
        console.log('Clicking at coordinates:', clickX, clickY);
        console.log('Viewport size:', viewportWidth, 'x', viewportHeight);
        
        // Create and dispatch click event at calculated coordinates
        var clickEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true,
          clientX: clickX,
          clientY: clickY,
          screenX: clickX,
          screenY: clickY
        });
        
        // Try to find the element at the coordinates and click it
        var elementAtPoint = document.elementFromPoint(clickX, clickY);
        if (elementAtPoint) {
          console.log('Element found at coordinates:', elementAtPoint.tagName, elementAtPoint.className);
          elementAtPoint.dispatchEvent(clickEvent);
          
          // Also try direct click if it's a clickable element
          if (elementAtPoint.click && typeof elementAtPoint.click === 'function') {
            elementAtPoint.click();
          }
        } else {
          // Fallback: dispatch event on document body
          console.log('No element found at coordinates, clicking on body');
          document.body.dispatchEvent(clickEvent);
        }
        
        return {
          clickX: clickX,
          clickY: clickY,
          elementFound: !!elementAtPoint,
          elementTag: elementAtPoint ? elementAtPoint.tagName : null
        };
      }, [], function(result) {
        console.log('Click execution result:', result.value);
      })
      
      // Wait for potential navigation/page changes
      .pause(3000)
      
      // Verify that navigation occurred by checking URL changed from welcome page
      .url(function(result) {
        var currentUrl = result.value;
        console.log('Current URL after click:', currentUrl);
        
        // Check if URL has changed from the initial welcome page
        if (this.initialUrl && currentUrl !== this.initialUrl) {
          console.log('✓ Navigation detected - URL changed from:', this.initialUrl, 'to:', currentUrl);
        } else {
          console.log('⚠ No navigation detected - URL remains:', currentUrl);
        }
      })
      
      // Assert that we're no longer on the welcome page
      .perform(function() {
        // Custom assertion to verify navigation occurred
        this.url(function(result) {
          var currentUrl = result.value;
          var isStillOnWelcome = currentUrl.includes('/welcome');
          
          if (!isStillOnWelcome) {
            console.log('✓ Test PASSED: Successfully navigated away from welcome page');
          } else {
            console.log('✗ Test WARNING: Still on welcome page, navigation may not have occurred');
          }
        });
      })
      
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    // Set viewport size to match configuration
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    // Allow time for cleanup
    setTimeout(function() {
      done();
    }, 2000);
  },
};
