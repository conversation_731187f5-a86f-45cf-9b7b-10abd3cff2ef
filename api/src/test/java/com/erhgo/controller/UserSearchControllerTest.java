package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserNote;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.UsersExportRequestDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;

import static com.erhgo.generators.TestFixtures.E_02_SOGILIS;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class UserSearchControllerTest extends AbstractIntegrationTest {

    @MockitoBean
    private MailingListService mailingListService;

    @MockitoBean
    private KeycloakMockService keycloakService;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private ApplicationContext applicationContext;

    private static final String ORGANIZATION_CODE = "E-042", TO_CODE = "T-043", TO2_CODE = "T-048", P_CODE = "P-044";

    @BeforeEach
    void prepareMock() {
        Mockito.when(keycloakService.getRolesForGroup(ORGANIZATION_CODE)).thenReturn(Set.of(ORGANIZATION_CODE));
        Mockito.when(keycloakService.getRolesForGroup(TO_CODE)).thenReturn(Set.of(TO_CODE, P_CODE));
        Mockito.when(keycloakService.getRolesForGroup(P_CODE)).thenReturn(Set.of(P_CODE));
        organizationGenerator.createRecruiter(TO_CODE, AbstractOrganization.OrganizationType.TERRITORIAL);
        organizationGenerator.createRecruiter(P_CODE, AbstractOrganization.OrganizationType.PROJECT);
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_as_admin_should_succeed() throws Exception {

        var query = "TestQuery";
        var organizationCode = ORGANIZATION_CODE;

        mvc.perform(getListByGroupRequestBuilder(200)
                        .param("query", query)
                        .param("organizationCodes", organizationCode))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void searchUser_as_OT_retrieves_multiple_channels_users_showing_only_authorized_channels() throws Exception {

        prepareUsersWithChannel();

        mvc.perform(getListByGroupRequestBuilder(200)
                        .param("organizationCodes", ORGANIZATION_CODE))
                .andExpect(TestUtils.jsonMatchesContent("userFrontList"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_with_query() throws Exception {

        prepareUsersWithChannel();

        mvc.perform(getListByGroupRequestBuilder(1)
                        .param("organizationCodes", ORGANIZATION_CODE)
                        .param("search", "twoChannel"))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder("twoChannels@localhost")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(1)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(1)))
        ;

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_with_query_and_postcode() throws Exception {

        prepareUsersWithChannel();

        mvc.perform(getListByGroupRequestBuilder(1)
                        .param("organizationCodes", ORGANIZATION_CODE)
                        .param("search", "channel")
                        .param("postcode", "17"))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder("oneChannel@localhost")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(1)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(1)))
        ;
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_with_postcode() throws Exception {

        prepareUsersWithChannel();

        mvc.perform(getListByGroupRequestBuilder(1)
                        .param("organizationCodes", ORGANIZATION_CODE)
                        .param("postcode", "18"))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder("twoChannels@localhost")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(1)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(1)))
        ;
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_with_multiple_orga() throws Exception {

        prepareUsersWithChannel();

        mvc.perform(getListByGroupRequestBuilder(10)
                        .param("organizationCodes", ORGANIZATION_CODE, P_CODE))
                .andExpect(jsonPath("$.content[*].contactInformation.email", containsInAnyOrder("oneChannel@localhost", "twoChannels@localhost", "oneOT@localhost", "twoOT@localhost")))
                .andExpect(jsonPath("$.totalNumberOfElements", is(4)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(4)))
        ;
    }


    private MockHttpServletRequestBuilder getListByGroupRequestBuilder(int pageSize) {
        return MockMvcRequestBuilders.get("/api/odas/user/front-office/list-by-group")
                .param("page", "0")
                .param("size", "" + pageSize);
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_as_ADMIN_retrieves_multiple_channels_users() throws Exception {

        searchUser_check_progress_common().andExpect(jsonPath("$.content[0].userProfileProgress.candidaturesCount", Matchers.is(1)));
    }

    private ResultActions searchUser_check_progress_common() throws Exception {
        organizationGenerator.createRecruiter(ORGANIZATION_CODE);
        var otherChannel = "E-444";
        var recruiter = organizationGenerator.createRecruiter(otherChannel);
        var userWithChannel = createUserRepresentation(false, "oneChannel", 5, false, null, ORGANIZATION_CODE, otherChannel);
        var userProfile = userProfileGenerator.createUserProfileForUserLocationAndPhone(userWithChannel.getId(), null, null, null, ORGANIZATION_CODE, otherChannel);

        var capacity = capacityGenerator.createCapacity("CA1-01");
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter, "J-042", capacity);
        candidatureGenerator.createCandidature(userProfile, recruitment);

        when(keycloakService.getFrontOfficeUserProfile(userWithChannel.getId()))
                .thenReturn(Optional.of(userWithChannel));

        return mvc.perform(getListByGroupRequestBuilder(200)
                        .param("organizationCodes", ORGANIZATION_CODE))
                .andExpect(TestUtils.jsonMatchesContent("userFrontListWithCandidature"));
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_as_ADMIN_filtered_strictly_by_code() throws Exception {
        var projectCode = "P-420";
        organizationGenerator.createRecruiter(projectCode, AbstractOrganization.OrganizationType.PROJECT);
        var orga = organizationGenerator.createRecruiter(ORGANIZATION_CODE);

        when(keycloakService.getRolesForGroup(ORGANIZATION_CODE)).thenReturn(Set.of(ORGANIZATION_CODE, projectCode));

        var userOfOrga = createUserRepresentation(true, "ORGA", 0, false, null, ORGANIZATION_CODE);
        createUserRepresentation(true, "PROJ", 1, false, null, projectCode);
        var userOfBoth = createUserRepresentation(true, "BOTH", 2, false, null, projectCode, ORGANIZATION_CODE);

        mvc.perform(getListByGroupRequestBuilder(200)
                        .param("organizationCodes", ORGANIZATION_CODE)
                        .param("strictOrganizationFilter", "true"))
                .andExpect(jsonPath("$.content[*].id", containsInAnyOrder(userOfOrga.getId(), userOfBoth.getId())));

        // Fookeeper
        mvc.perform(getListByGroupRequestBuilder(3)
                        .param("organizationCodes", ORGANIZATION_CODE))
                .andExpect(jsonPath("$.content[*]", hasSize(3)))
                .andExpect(jsonPath("$.totalNumberOfElements", is(3)))
                .andExpect(jsonPath("$.numberOfElementsInPage", is(3)))
        ;
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void searchUser_with_finished_registration_state_as_ADMIN() throws Exception {
        var occupationId = UUID.fromString("e2e1ab74-9f3e-4d03-b997-887b40f27704");
        var users = new ArrayList<UserRepresentation>();
        txHelper.doInTransaction(() ->
        {
            var occupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject().withId(occupationId).withTitle("Pompière").instance());
            var stepValues = UserRegistrationState.RegistrationStep.values();
            IntStream.range(0, stepValues.length).forEach(i -> {
                var userId = UUID.fromString("e2e1ab74-9f3e-4d03-b997-887b40f277%02d".formatted(i));
                var user = userProfileGenerator.createUserProfile(userId);
                user.generalInformation().setSalary(2500);
                user.generalInformation().setSituation(Situation.EMPLOYEE);
                user.userRegistrationState(UserRegistrationState.builder()
                        .registrationStep(stepValues[i])
                        .jobTitle("Un métier bien connu")
                        .selectedOccupation(occupation)
                        .build());
                var userRepresentation = new UserRepresentation();
                userRepresentation.setEmail(userId + "@yolo.com");
                userRepresentation.setId(userId.toString());
                users.add(userRepresentation);
            });
        });
        when(keycloakService.searchFrontOfficeUsersPaginated(ArgumentMatchers.nullable(String.class), ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt())).thenReturn(new PageImpl<>(users));

        performGetAndExpect("/user/front-office/list?size=100&page=0", "userFrontListWithFinishedRegistration", false);

    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void search_user_candidatures_hide_non_finalized() throws Exception {
        var userWithChannel = createUserRepresentation(false, "oneChannel", 5, false, null, Role.ODAS_ADMIN);
        var userProfile = userProfileGenerator.createUserProfileForUserLocationAndPhone(userWithChannel.getId(), null, null, null, Role.ODAS_ADMIN);
        add_3_candidatures_on_2_different_organizations_for_user_profile(userProfile);
        txHelper.doInTransaction(() -> {
            recruitmentCandidatureRepository.findAll().forEach(c -> c.setState(CandidatureState.STARTED));
        });

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/" + userProfile.userId() + "/matching-jobs/candidatures")
                        .param("organizationCode", TO2_CODE))
                .andExpect(jsonPath("$[*]", hasSize(0)));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void search_user_candidatures_as_admin() throws Exception {
        var userWithChannel = createUserRepresentation(false, "oneChannel", 5, false, null, Role.ODAS_ADMIN);
        var userProfile = userProfileGenerator.createUserProfileForUserLocationAndPhone(userWithChannel.getId(), null, null, null, Role.ODAS_ADMIN);
        add_3_candidatures_on_2_different_organizations_for_user_profile(userProfile);

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/%s/matching-jobs/candidatures".formatted(userProfile.userId()))
                        .param("organizationCode", TO2_CODE))
                .andExpect(TestUtils.jsonMatchesContent("userCandidaturesAsAdmin"));
    }


    private void add_3_candidatures_on_2_different_organizations_for_user_profile(UserProfile userProfile) {
        Mockito.when(keycloakService.getRolesForGroup(TO2_CODE)).thenReturn(Set.of(TO2_CODE));

        var recruiter1 = organizationGenerator.createRecruiter(TO2_CODE, AbstractOrganization.OrganizationType.TERRITORIAL);
        var capacity1 = capacityGenerator.createCapacity("CA1-01");
        var recruitment1 = recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter1, "J-043", capacity1);

        var recruiter2 = organizationGenerator.createRecruiter("E-044", AbstractOrganization.OrganizationType.ENTERPRISE);
        var capacity2 = capacityGenerator.createCapacity("CA1-02");
        var recruitment2 = recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter2, "J-044", capacity2);
        var recruitment3 = recruitmentGenerator.createRecruitmentWithNoRequirement(recruiter2, "J-045", capacity2);

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment1)
                .generated(true)
                .withRefusalData(CandidatureEmailRefusalState.NONE, "Alfred")
                .withNoteAtDate("Plouf", LocalDateTime.of(2020, 5, 25, 2, 5))
                .buildAndPersist();

        applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment3)
                .generated(true)
                .withModifiedByUser(true)
                .withRefusalData(CandidatureEmailRefusalState.NONE, "Alfred")
                .withNoteAtDate("Plouf", LocalDateTime.of(2020, 5, 25, 2, 5))
                .buildAndPersist();

        candidatureGenerator.createCandidature(userProfile, recruitment2);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Search user as admin should retrieves all users with all channels")
    void searchAsAdmin() throws Exception {
        prepareUsersWithChannel();

        performGetAndExpect("/user/front-office/list?size=10&page=0", "userFrontListAdmin", false);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Search user should paginate first page")
    void searchPaginate1() throws Exception {
        prepareUsersWithChannel();
        createUserRepresentation(true, "another", 9, false, null, ORGANIZATION_CODE);

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/front-office/list-by-group")
                        .param("page", "0")
                        .param("size", "2")
                        .param("organizationCodes", ORGANIZATION_CODE))
                .andExpect(jsonPath("$.totalNumberOfElements", Matchers.is(3)))
                .andExpect(jsonPath("$.totalPages", Matchers.is(2)))
                .andExpect(jsonPath("$.content[*]", hasSize(2)));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @DisplayName("Search user should paginate last page")
    void searchPaginate2() throws Exception {
        prepareUsersWithChannel();
        createUserRepresentation(true, "another", 9, false, null, ORGANIZATION_CODE);

        mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/front-office/list-by-group")
                        .param("page", "1")
                        .param("size", "2")
                        .param("organizationCodes", ORGANIZATION_CODE))
                .andExpect(jsonPath("$.totalNumberOfElements", Matchers.is(3)))
                .andExpect(jsonPath("$.totalPages", Matchers.is(2)))
                .andExpect(jsonPath("$.content[*]", hasSize(1)));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportCsv_should_succeed() throws Exception {
        prepareUser();
        txHelper.doInTransaction(() -> {
            entityManager.createNativeQuery("UPDATE UserNote set UserNote.updatedDate = :date").setParameter("date", LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0)).executeUpdate();

        });
        var command = new UsersExportRequestDTO().deanonymizedUser(false).usersId(Collections.emptyList());

        mvc.perform(MockMvcRequestBuilders.post("/api/odas/user/front-office/export").content(objectMapper.writeValueAsBytes(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(TestUtils.stringMatchesContent("usersExport.csv"));

        Mockito.verify(keycloakService).findAllFrontOfficeUsers();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportCsv_should_succeed_deanonymized_with_all_values() throws Exception {
        prepareUser();
        var command = new UsersExportRequestDTO().deanonymizedUser(true).usersId(Collections.emptyList());

        mvc.perform(MockMvcRequestBuilders.post("/api/odas/user/front-office/export").content(objectMapper.writeValueAsBytes(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(TestUtils.stringMatchesContent("usersExportDeanonymized.csv"));

        Mockito.verify(keycloakService).findAllFrontOfficeUsers();
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportCsv_tolerate_sib_unavailable() throws Exception {
        Mockito.when(mailingListService.getListUsersJobDating(anyLong(), anyLong())).thenThrow(GenericTechnicalException.class);
        var command = new UsersExportRequestDTO().deanonymizedUser(false).usersId(Collections.emptyList());

        mvc.perform(MockMvcRequestBuilders.post("/api/odas/user/front-office/export").content(objectMapper.writeValueAsBytes(command)).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(TestUtils.stringMatchesContent("usersExportSibTolerant.csv"));

        Mockito.verify(keycloakService).findAllFrontOfficeUsers();
    }

    private void prepareUser() {
        txHelper.doInTransaction(() -> {
            var user = createUserRepresentation(true, "registrationFinished", 8, true, null);
            var userProfile = userProfileRepository.findByUserId(user.getId()).orElseThrow();
            var note = UserNote.builder()
                    .userProfile(userProfile)
                    .organization(E_02_SOGILIS)
                    .content("Test note for user")
                    .build();
            var xp = UserExperience.builder()
                    .type(ExperienceType.JOB)
                    .jobTitle("Job Title Nb 1")
                    .durationInMonths(3)
                    .organizationName("Organization Nb 1")
                    .userProfile(userProfile)
                    .build();
            userProfile.generalInformation().setDelayInMonth(3);
            userProfile.generalInformation().setSmsBlacklisted(true);
            userProfile.generalInformation().setPhoneNumber("0454758545");
            userProfile.lastConnectionDate(LocalDateTime.of(2020, 8, 10, 0, 0, 0, 0));
            var occupation = erhgoOccupationGenerator.createErhgoOccupation("Pizzaiolo",
                    MasteryLevel.PROFESSIONAL,
                    true,
                    Collections.emptySet(),
                    Collections.emptySet()
            );
            var userRegistrationState = userProfile.userRegistrationState();
            userRegistrationState.setSelectedOccupation(occupation);
            userRegistrationState.setJobTitle(occupation.getTitle());
            userProfile.generalInformation().setSalary(4000);
            userProfile.generalInformation().setSituation(Situation.RESEARCHING);
            userProfileGenerator.prepareCriteria(userProfile, DriverLicence.LICENCE_B);
            userProfile.updateJobOfferOptOut(true);
            userProfile.experiences().add(xp);
            Mockito.when(mailingListService.getListUsersJobDating(anyLong(), anyLong())).thenReturn(List.of(new MailingUserDTO().setEmail(user.getEmail())));
            userProfile.userNotes().add(note);
            prepareUsersWithChannel(user);
        });
    }

    private void prepareUsersWithChannel(UserRepresentation... otherUsers) {
        organizationGenerator.createRecruiter(ORGANIZATION_CODE);

        var userWithoutProfile = createUserRepresentation(true, "noProfile", 0, true, null);
        var userWithoutLocation = createUserRepresentation(true, "noLocation", 1, false, null);
        var userWithLocation = createUserRepresentation(false, "location", 2, false, "18000");
        var userWithChannel = createUserRepresentation(true, "oneChannel", 3, false, "17000", ORGANIZATION_CODE);
        var userWithOneUnknownChannel = createUserRepresentation(true, "twoChannels", 4, false, "18000", ORGANIZATION_CODE, "E-UnKnown");
        var userWithoutInformation = createUserRepresentation(false, "noInformation", 5, false, null);
        var userWithOneOTChannel = createUserRepresentation(true, "oneOT", 6, false, null, P_CODE);
        var userWithTwoOTChannel = createUserRepresentation(true, "twoOT", 7, false, null, TO_CODE, P_CODE);

        userProfileGenerator.createUserProfileForUserLocationAndPhone(userWithLocation.getId(), "Lille", "59000", "0328201522");
        userProfileGenerator.createUserProfileWithoutInformation(userWithoutInformation);

        var result = Lists.newArrayList(userWithoutProfile, userWithoutLocation, userWithLocation, userWithChannel, userWithOneUnknownChannel, userWithoutInformation, userWithOneOTChannel, userWithTwoOTChannel);
        if (otherUsers.length != 0) {
            result.addAll(List.of(otherUsers));
        }
        when(keycloakService.findAllFrontOfficeUsers()).thenReturn(result);
        when(keycloakService.searchFrontOfficeUsersPaginated(ArgumentMatchers.nullable(String.class), ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt())).thenReturn(new PageImpl<>(result));
    }

    private UserRepresentation createUserRepresentation(boolean save, String prefix, int index, boolean withSelectedOccupation, String postcode, String... channels) {
        var userRepresentation = new UserRepresentation();
        userRepresentation.setEmail(prefix + "@localhost");
        userRepresentation.setFirstName(prefix + "Jean");
        userRepresentation.setLastName(prefix + "Dupont");
        userRepresentation.setId("45f3ce6c-f1c9-4356-9d07-0bca1b24140" + index);
        userRepresentation.setCreatedTimestamp(index == 0 ? null : index * 100_000_000L);

        if (save) {
            if (withSelectedOccupation) {
                var occupation = erhgoOccupationGenerator.createErhgoOccupation("Pizzaiolo",
                        MasteryLevel.PROFESSIONAL,
                        true,
                        Collections.emptySet(),
                        Collections.emptySet()
                );
                userProfileGenerator.createUserProfileForUserLocationAndPhoneAndSelectedOccupation(userRepresentation.getId(), postcode, null, null, occupation, channels);

            } else {
                userProfileGenerator.createUserProfileForUserLocationAndPhone(userRepresentation.getId(), postcode, null, null, channels);
            }
        }
        when(keycloakService.getFrontOfficeUserProfile(userRepresentation.getId())).thenReturn(Optional.of(userRepresentation));
        return userRepresentation;
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void exportUsersByGroupCsv_admin_should_succeed() throws Exception {
        exportByGroupCommon()
                .andExpect(TestUtils.stringMatchesContent("usersByGroupExportAsAdmin.csv"));
    }

    private ResultActions exportByGroupCommon() throws Exception {
        txHelper.doInTransaction(() -> {
            var user = createUserRepresentation(true, "registrationFinished", 8, true, null, P_CODE);
            var userProfile = userProfileRepository.findByUserId(user.getId()).orElseThrow();
            userProfile.generalInformation().setDelayInMonth(3);
            userProfile.generalInformation().setSmsBlacklisted(true);
            userProfile.generalInformation().setPhoneNumber("0454758545");
            var occupation = erhgoOccupationGenerator.createErhgoOccupation("Pizzaiolo",
                    MasteryLevel.PROFESSIONAL,
                    true,
                    Collections.emptySet(),
                    Collections.emptySet()
            );
            var userRegistrationState = userProfile.userRegistrationState();
            userRegistrationState.setSelectedOccupation(occupation);
            userRegistrationState.setJobTitle(occupation.getTitle());
            userProfile.generalInformation().setSalary(4000);
            userProfile.generalInformation().setSituation(Situation.RESEARCHING);
            userProfileGenerator.prepareCriteria(userProfile, DriverLicence.LICENCE_B);
            userProfile.updateJobOfferOptOut(true);
            prepareUsersWithChannel(user);
        });
        return mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/front-office/list-by-group/export")
                        .param("query", "")
                        .param("organizationCode", P_CODE))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void getHandicapUsersInfo() {
        generateHandicapUsers();
        performGetAndExpect("/user/handicap-info/list", "userHandicapList", false);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void getHandicapUsersExport() {
        generateHandicapUsers();
        mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/front-office/simple/export"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andExpect(header().string("Content-Disposition", containsString("attachment")))
                .andExpect(header().string("Content-Disposition", containsString("utilisateurs_simple_")));
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void getHandicapUsersExport_should_include_all_users_not_only_handicap() {
        generateHandicapUsers();

        IntStream.range(0, 2)
                .forEach(i -> {
                    applicationContext.getBean(UserProfileMotherObject.class)
                            .withUserId(UUID.randomUUID().toString())
                            .withFirstname("Normal" + i)
                            .withLastname("User" + i)
                            .withEmail("normal-" + i + "@example.com")
                            .buildAndPersist();
                });

        var result = mvc.perform(MockMvcRequestBuilders.get("/api/odas/user/front-office/simple/export"))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("text/csv"))
                .andReturn();

        var header = result.getResponse().getContentAsString().split("\n")[0];
        assertTrue(header.contains("Identifiant"));
        assertTrue(header.contains("Date de création"));
        assertTrue(header.contains("Prescripteur"));
        assertTrue(header.contains("Source"));
        assertTrue(header.contains("jenesuispasunhandicap origine O/N"));
        assertTrue(header.contains("jenesuispasunhandicap actif O/N"));
    }


    private void generateHandicapUsers() {
        var handicapUsers = new ArrayList<UserProfile>();
        // Create users with isFromHandicap = true
        IntStream.range(0, 3)
                .forEach(i -> {
                    var user = applicationContext.getBean(UserProfileMotherObject.class)
                            .withUserId(UUID.randomUUID().toString())
                            .withFirstname("HandicapOrigin" + i)
                            .withLastname("User" + i)
                            .withEmail("handicap-origin-" + i + "@example.com")
                            .withIsFromHandicap(true)
                            .buildAndPersist();
                    handicapUsers.add(user);
                });

        // Create users with handicapModeEnabled = true
        IntStream.range(0, 2)
                .forEach(i -> {
                    var user = applicationContext.getBean(UserProfileMotherObject.class)
                            .withUserId(UUID.randomUUID().toString())
                            .withFirstname("HandicapEnabled" + i)
                            .withLastname("User" + i)
                            .withEmail("handicap-enabled-" + i + "@example.com")
                            .withHandicapModeEnabled(true)
                            .buildAndPersist();
                    handicapUsers.add(user);
                });

        // Create users with both flags
        IntStream.range(0, 1)
                .forEach(i -> {
                    var user = applicationContext.getBean(UserProfileMotherObject.class)
                            .withUserId(UUID.randomUUID().toString())
                            .withFirstname("HandicapBoth" + i)
                            .withLastname("User" + i)
                            .withEmail("handicap-both-" + i + "@example.com")
                            .withIsFromHandicap(true)
                            .withHandicapModeEnabled(true)
                            .buildAndPersist();
                    handicapUsers.add(user);
                });

    }

}
