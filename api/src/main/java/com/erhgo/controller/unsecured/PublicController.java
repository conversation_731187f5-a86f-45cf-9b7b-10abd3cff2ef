package com.erhgo.controller.unsecured;

import com.erhgo.config.ApiConstants;
import com.erhgo.controller.MobileHackForContractTypeUtils;
import com.erhgo.openapi.controller.PublicApi;
import com.erhgo.openapi.dto.LogEventDTO;
import com.erhgo.openapi.dto.RecruitmentDetailDTO;
import com.erhgo.services.HandicapAccountService;
import com.erhgo.services.LandingPageService;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.userprofile.UserProfileService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
@Slf4j
public class PublicController implements PublicApi {
    @Value("${version:unknown}")
    private String version;

    private final ObjectMapper objectMapper;
    private final RecruitmentService recruitmentService;
    private final LandingPageService landingPageService;
    private final UserProfileService userProfileService;
    private final SecurityService securityService;
    private final HandicapAccountService handicapAccountService;
    private final HttpServletRequest request;

    @Override
    public ResponseEntity<RecruitmentDetailDTO> getPublicRecruitmentInfo(String recruitmentCode) {
        return ResponseEntity.ok(MobileHackForContractTypeUtils.hackForMobile(recruitmentService.findDetailByCode(recruitmentCode), request));
    }

    @Override
    public ResponseEntity<Void> logEvent(LogEventDTO logEventDTO) {
        log.error(logEventDTO.getMessage());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<String> getVersion() {
        return ResponseEntity.ok(version);
    }

    @Override
    public ResponseEntity<String> getLandingPageByUrlKey(String urlKey) {
        return ResponseEntity.ok().body(landingPageService.getContentForKey(urlKey));
    }

    private String extractAsString(Map<String, Object> result) {
        return Optional.ofNullable(result.get("metadatas"))
                .filter(String.class::isInstance)
                .map(m -> {
                    try {
                        return objectMapper.readValue((String) m, Map.class);
                    } catch (JsonProcessingException e) {
                        log.warn("unable to parse trimoji result", e);
                        return null;
                    }
                }).map(m -> String.valueOf(m.get("candidate_id")))
                .orElse(null);
    }

    @NotNull
    private static Optional<String> extractAsObject(Map<String, Object> result) {
        return Optional.ofNullable(result.get("metadatas")).filter(Map.class::isInstance).map(o -> String.valueOf(((Map<?, ?>) o).get("candidate_id")));
    }

    @Override
    public ResponseEntity<Void> postTrimoji(Map<String, Object> result) {
        try {
            log.debug("Got TRIMOJI result {}", objectMapper.writeValueAsString(result));
        } catch (JsonProcessingException e) {
            log.error("Unable to stringify {}", result);
        }
        var userId = extractAsObject(result)
                .orElseGet(() -> extractAsString(result));
        if (userId != null) {
            Optional.ofNullable(result.get("result_pdf"))
                    .ifPresentOrElse(
                            url -> securityService.doAsAdmin(() -> userProfileService.updateTrimojiPdffUrl(userId, String.valueOf(url))),
                            () -> log.error("No PDF in metadata of trimoji result for user {}", userId));
        } else {
            log.error("No candidate_id in metadata of Trimoji result");
        }
        return ResponseEntity.noContent().build();
    }

    @PostMapping(value = "/public/handicap-account/4203e19815-7665-4d5e-b9bd-05c49989893c24", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> createAccount(@RequestBody String jsonPayload) {
        securityService.doAsAdmin(() -> handicapAccountService.createAccount(jsonPayload));
        return ResponseEntity.ok().build();
    }
}
