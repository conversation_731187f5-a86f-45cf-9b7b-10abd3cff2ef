package com.erhgo.utils;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.userprofile.FilePartProvider;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.MediaType;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ImageUtils {
    private static final float COMPRESSION_QUALITY = 0.5f;
    private static final int MIN_SIZE_REQUIRED_FOR_COMPRESSION = 500 * 1024;

    public static byte[] compressImage(InputStreamSource inputStreamSource) throws IOException {
        try (var originalInputStream = inputStreamSource.getInputStream()) {
            var data = originalInputStream.readAllBytes();
            if (data.length <= MIN_SIZE_REQUIRED_FOR_COMPRESSION) {
                return data;
            }
            var inputImage = ImageIO.read(new ByteArrayInputStream(data));
            if (inputImage == null) {
                throw new GenericTechnicalException("Failed to read image data");
            }
            var grayImage = convertToGrayscale(inputImage);
            return compressToJpeg(grayImage);
        }
    }

    static BufferedImage convertToGrayscale(BufferedImage image) {
        int width = image.getWidth() / 2;
        int height = image.getHeight() / 2;

        var result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        result.getGraphics().drawImage(image, 0, 0, width, height, null);

        return result;
    }

    static byte[] compressToJpeg(BufferedImage image) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        var writer = ImageIO.getImageWritersByFormatName("jpg").next();
        var ios = ImageIO.createImageOutputStream(outputStream);
        writer.setOutput(ios);

        var param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(COMPRESSION_QUALITY);

        writer.write(null, new IIOImage(image, null, null), param);

        writer.dispose();
        ios.close();

        return outputStream.toByteArray();
    }

    public static MediaType getMediaType(FilePartProvider file) {
        var contentType = file.contentType();
        if (contentType == null || contentType.isBlank()) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }

        try {
            return MediaType.parseMediaType(contentType);
        } catch (Exception e) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }

    public static boolean isImage(FilePartProvider file) {
        var mediaType = getMediaType(file);
        return mediaType.getType().equalsIgnoreCase("image");
    }

    public static boolean isPdf(FilePartProvider file) {
        var mediaType = getMediaType(file);
        return mediaType.toString().equalsIgnoreCase("application/pdf");
    }
}
