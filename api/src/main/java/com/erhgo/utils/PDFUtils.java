package com.erhgo.utils;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.exceptions.UnsupportedMediaTypeException;
import com.erhgo.services.userprofile.FilePartProvider;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.IOException;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class PDFUtils {

    private static final long MAX_FILE_SIZE = 10L * 1024 * 1024; // 10 MB
    private static final int MAX_WORDS_ALLOWED = 6000;

    public static String extractTextFromPDF(@NotNull FilePartProvider file) {
        validatePdfFile(file);
        try (var inputStream = file.getInputStream();
             var document = Loader.loadPDF(inputStream.readAllBytes())) {
            return truncateText(new PDFTextStripper().getText(document));
        } catch (IOException e) {
            throw new GenericTechnicalException("unable to read multipart file", e);
        }
    }

    private static void validatePdfFile(FilePartProvider file) {
        if (file == null || file.isEmpty()) {
            log.error("Uploaded file is empty");
            throw new GenericTechnicalException("File is empty");
        }
        if (file.contentType() == null || !file.contentType().contains("application/pdf")) {
            throw new UnsupportedMediaTypeException(file.contentType());
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            log.error("Uploaded file is too big");
            throw new GenericTechnicalException("File size exceeds the maximum allowed size of %d MB".formatted(MAX_FILE_SIZE / (1024 * 1024)));
        }
    }

    private static String truncateText(String text) {
        var words = text.split("\\s+");
        if (words.length > MAX_WORDS_ALLOWED) {
            var truncatedText = new StringBuilder();
            for (int i = 0; i < MAX_WORDS_ALLOWED; i++) {
                truncatedText.append(words[i]).append(" ");
            }
            return truncatedText.toString().trim();
        }
        return text;
    }
}
