package com.erhgo.services.http;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.context.annotation.Scope;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service()
@Scope("prototype")
public class RetryableHttpClient {

    private final OkHttpClient httpClient;

    public RetryableHttpClient() {
        this.httpClient = createHttpClient(30, 30, 30);
    }

    public RetryableHttpClient(int connectTimeoutSeconds, int readTimeoutSeconds, int writeTimeoutSeconds) {
        this.httpClient = createHttpClient(connectTimeoutSeconds, readTimeoutSeconds, writeTimeoutSeconds);
    }

    private static OkHttpClient createHttpClient(int connectTimeoutSeconds, int readTimeoutSeconds, int writeTimeoutSeconds) {
        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeoutSeconds, TimeUnit.SECONDS)
                .readTimeout(readTimeoutSeconds, TimeUnit.SECONDS)
                .writeTimeout(writeTimeoutSeconds, TimeUnit.SECONDS)
                .build();
    }

    @Retryable(
            retryFor = HttpRetryableException.class,
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000L, multiplier = 2.0),
            label = "Execute HTTP Request"
    )
    public Response executeRequestWithStatusCheck(Request request) {
        try {
            var response = httpClient.newCall(request).execute();
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    log.warn("File not found (404) for URL: {} - will retry", request.url());
                } else if (response.code() >= 500) {
                    log.warn("Server error ({}) for URL: {} - will retry", response.code(), request.url());
                } else {
                    log.warn("Client error ({}) for URL: {} - will retry", response.code(), request.url());
                }
                throw new HttpRetryableException("HTTP error: " + response.code() + " for URL: " + request.url());
            }
            return response;
        } catch (IOException e) {
            log.warn("IO error during HTTP request execution: {}", e.getMessage(), e);
            throw new HttpRetryableException("IO error during request execution", e);
        }
    }

    @Retryable(
            retryFor = HttpRetryableException.class,
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000L, multiplier = 2.0),
            label = "Execute HTTP Request"
    )
    public Response executeRequest(Request request) {
        try {
            var response = httpClient.newCall(request).execute();
            if (response.code() >= 500) {
                throw new HttpRetryableException("Server error: " + response.code());
            }
            return response;
        } catch (IOException e) {
            log.warn("IO error during HTTP request execution: {}", e.getMessage(), e);
            throw new HttpRetryableException("IO error during request execution", e);
        }
    }

    public static class HttpRetryableException extends GenericTechnicalException {
        public HttpRetryableException(String message) {
            super(message);
        }

        public HttpRetryableException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
