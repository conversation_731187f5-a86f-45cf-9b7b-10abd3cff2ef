package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.erhgo.utils.StringUtils;
import com.jayway.jsonpath.JsonPath;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
@RequiredArgsConstructor
public class HandicapAccountService {

    private final KeycloakService keycloakService;
    private final UserProfileProvider userProfileProvider;
    private final CVDataExtractorService cvDataExtractorService;
    private final UserProfileCompetencesExportService userProfileCompetencesExportService;
    private final MailingListService mailingListService;
    private final RetryableHttpClient handicapRetryableHttpClient;

    @Value("${sendinblue.templates.handicap-account-creation-with-profile}")
    private Long handicapWithProfileEmailTemplateId;
    @Value("${sendinblue.templates.handicap-account-creation-without-profile}")
    private Long handicapWithoutProfileEmailTemplateId;

    @Async
    public void createAccount(String jsonPayload) {
        var email = extractEmail(jsonPayload);
        var fileUrl = extractFileUrl(jsonPayload);
        createOrUpdateUserForFileURL(email, fileUrl);
    }

    private void createOrUpdateUserForFileURL(String email, String fileUrl) {
        createOrUpdateUserForFileURL(email, fileUrl, false);
    }

    public String createOrUpdateUserForFileURL(String email, String fileUrl, boolean suppressEmailNotifications) {
        var password = StringUtils.generateRandomPassword();
        var userId = getOrCreateUserId(email, password);
        try {
            handleCv(fileUrl, email, userId);
            return "Traité avec succès - fichier trouvé et traité";
        } catch (RuntimeException ex) {
            log.error("CV processing failed for user {} with email {}", userId, email, ex);
            return "Échec du traitement - %s".formatted(ex.getMessage());
        } finally {
            if (!suppressEmailNotifications) {
                handlePostCVProcessing(userId, email, password);
            }
        }
    }

    private String getOrCreateUserId(String email, String password) {
        var userRepresentation = new UserKeycloakRepresentation()
                .setEmail(email)
                .setPassword(password);
        String userId;
        try {
            userId = keycloakService.createUserInFrontOfficeRealm(userRepresentation);
            log.info("Created handicap account for user with ID: {}", userId);
        } catch (EntityAlreadyExistException e) {
            log.info("Account {} already exists in KC", userRepresentation.getEmail(), e);
            userId = keycloakService.getFOUserRepresentationByEmail(userRepresentation.getEmail()).getId();
        }
        userProfileProvider.getUserProfileOrCreateForHandicap(userId);
        return userId;
    }

    private static String extractFileUrl(String jsonPayload) {
        var optionalFileUrl = getOptionalValueFromJson(jsonPayload, "file_url");
        return optionalFileUrl.orElse(null);
    }

    private static String extractEmail(String jsonPayload) {
        var optionalEmail = getOptionalValueFromJson(jsonPayload, "email");
        return optionalEmail
                .map(org.apache.commons.lang3.StringUtils::trimToNull)
                .orElseThrow(() -> {
                    log.warn("No email set on handicap account creation");
                    return new GenericTechnicalException("Email is required");
                });
    }

    private static @NotNull Optional<String> getOptionalValueFromJson(String jsonPayload, String key) {
        JSONArray email = JsonPath.read(jsonPayload, "$.form_response.answers[?(@.type=='" + key + "')]." + key);
        return Optional.ofNullable(email)
                .filter(e -> !e.isEmpty()).map(ArrayList::getFirst)
                .map(Object::toString)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank);
    }

    private void handleCv(@Nullable String fileUrl, String email, String userId) {
        var fileData = downloadCV(fileUrl, email);
        generateProfileForFileData(userId, email, fileUrl, fileData);
    }

    private void generateProfileForFileData(String userId, String email, String fileUrl, byte[] fileData) {
        if (fileData == null) {
            log.error("No file data downloaded for user {}", userId);
            return;
        }
        try (var inputStream = new ByteArrayInputStream(fileData)) {
            var cvExtraction = cvDataExtractorService.extractUserExperiencesFromCV(userId, new FilePartProvider(inputStream.readAllBytes(), fileUrl));
            log.debug("CV file processing for email: {}", email);
            cvExtraction.get();
            log.debug("CV file processed for email: {}", email);
        } catch (ExecutionException | IOException e) {
            log.error("Unable to process CV file {} for email: {}", fileUrl, email, e);
        } catch (InterruptedException e) {
            log.error("Interrupted thread when processing CV file {} for email: {}", fileUrl, email, e);
            Thread.currentThread().interrupt();
        }
    }

    private byte @Nullable [] downloadCV(String fileUrl, String email) {
        log.info("Downloading CV file from URL: {}", fileUrl);

        if (org.apache.commons.lang3.StringUtils.isBlank(fileUrl)) {
            log.warn("No file URL set on handicap account creation - got email {}", email);
            return null;
        } else {
            return downloadFile(fileUrl);
        }

    }


    private byte[] downloadFile(String fileUrl) {
        log.debug("Downloading file from URL: {}", fileUrl);
        try {
            var request = new okhttp3.Request.Builder()
                    .url(fileUrl)
                    .get()
                    .build();

            try (var response = handicapRetryableHttpClient.executeRequestWithStatusCheck(request)) {
                if (response == null || response.body() == null) {
                    log.error("Empty response body when downloading file from URL: {}", fileUrl);
                    return null;
                }
                var fileData = response.body().bytes();
                log.debug("Successfully downloaded file from URL: {}, size: {} bytes", fileUrl, fileData.length);
                return fileData.length == 0 ? null : fileData;
            }
        } catch (IOException e) {
            log.error("Error downloading file from URL: {} after retries", fileUrl, e);
            return null;
        }
    }

    private void handlePostCVProcessing(String userId, String email, String password) {
        try {
            log.info("CV processing completed for user with ID: {}", userId);

            var profileOutputStream = new ByteArrayOutputStream();
            userProfileCompetencesExportService.getUserProfileForUser(userId, profileOutputStream, false, true);
            sendProfileByEmail(email, profileOutputStream.toByteArray(), password);
        } catch (RuntimeException | IOException ioe) {
            log.info("Error during handicap account creation for user {}({}): {}", userId, email, ioe.getMessage(), ioe);
            sendEmailWithoutProfile(email, password);
        }
    }

    private void sendEmailWithoutProfile(String email, String password) {
        mailingListService.sendMailsForTemplate(
                Set.of(email),
                handicapWithoutProfileEmailTemplateId,
                Map.of("password", password, "login", email),
                null
        );
    }

    private void sendProfileByEmail(String email, byte[] profilePdf, String password) throws IOException {
        var filePartProvider = new FilePartProvider(
                profilePdf,
                "profile_competence.pdf",
                "application/pdf");

        mailingListService.sendMailsForTemplate(
                Set.of(email),
                handicapWithProfileEmailTemplateId,
                Map.of("password", password, "login", email),
                filePartProvider
        );
        log.info("Handicap profile sent to email: {}", email);
    }
}
