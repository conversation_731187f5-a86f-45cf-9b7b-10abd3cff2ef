package com.erhgo.services.mailing;

import com.erhgo.config.EnvironmentProfile;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserMobileTokenRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.EmailSendingNotifierMessageDTO;
import com.erhgo.services.reminder.EmailToNotifyFilterInterface;
import com.erhgo.services.userprofile.FilePartProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sendinblue.ApiException;
import sibModel.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnExpression("!'${sendinblue.apiKey}'.isEmpty()")
public class SendInBlueMailingListService implements MailingListService {

    public static final String LAST_CONNECTION_DATE_SENDING_BLUE_PROPERTY = "DATE_LAST_CX";
    private static final String PHONE_NUMBER_SENDINBLUE_PROPERTY = "sms";
    private static final String FIRST_NAME_SENDINBLUE_PROPERTY = "PRENOM";
    private static final String LAST_NAME_SENDINBLUE_PROPERTY = "NOM";
    private static final String CREATION_DATE_SENDINBLUE_PROPERTY = "DATE_CREATION";
    public static final String EMAIL_SENDINBLUE_PROPERTY = "EMAIL";
    public static final String LAST_CANDIDATURE_DATE_SENDINBLUE_PROPERTY = "DATE_LAST_APPLY";
    public static final String NUMBER_OF_EXPERIENCES_SENDINBLUE_PROPERTY = "NB_XP";
    public static final String SALARY_SENDINBLUE_PROPERTY = "SALARY";
    public static final String SITUATION_SENDINBLUE_PROPERTY = "SITUATION";
    public static final String USER_MOBILE_USAGE_SENDINBLUE_PROPERTY = "USER_MOBILE_USAGE";
    // The date format that Brevo (ex-SIB) is only accepting
    // No problem when creating contact, but it conflicts when updating with dd-MM-yyyy format
    //https://developers.brevo.com/docs/other-common-questions#:~:text=What%20is%20the%20date%20format,29
    public static final String SIB_SIMPLE_DATE_FORMAT = "yyyy-MM-dd";
    public static final String FAILED_TO_GET_SENDINBLUE_CONTACTS = "Failed to get contacts from SendInBlue";
    public static final String DEPARTMENT_SENDINBLUE_KEY = "DEPARTEMENT";
    public static final String REGION_SENDINBLUE_KEY = "REGION";
    public static final String POSTCODE_SENDINBLUE_KEY = "CODE_POSTAL";

    public static final Map<Situation, String> SITUATION_ENUM_MAPPING =
            Map.of(
                    Situation.RESEARCHING, "1",
                    Situation.STANDBY, "2",
                    Situation.EMPLOYEE, "3"
            );

    public static final Map<Boolean, String> USER_MOBILE_USAGE_ENUM_MAPPING =
            Map.of(
                    true, "1",
                    false, "2"
            );

    private final SendInBlueClientConfiguration clientConfiguration;
    private final KeycloakService keycloakService;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final UserMobileTokenRepository userMobileTokenRepository;
    private final UserProfileRepository userProfileRepository;
    private final EmailToNotifyFilterInterface emailToNotifyFilter;
    private final Notifier notifier;
    private final Environment environment;

    @Value("${sendinblue.jobOfferSender}")
    private String jobOfferSender;
    @Value("${sendinblue.list.default-new-user-list}")
    private Long defaultNewUserList;

    @Value("${sendinblue.maxRecipientsPerRequest}")
    private int maxRecipientsPerRequest;
    private final SimpleDateFormat sendingBlueLegacyDateFormatter = new SimpleDateFormat(SIB_SIMPLE_DATE_FORMAT);
    private final DateTimeFormatter sendingBlueLocaleDateTimeFormatter = DateTimeFormatter.ofPattern(SIB_SIMPLE_DATE_FORMAT);


    @PostConstruct
    public void initialize() {
        log.info("Initializing Sendinblue mailing service...");
    }

    @VisibleForTesting
    public void createContact(UserProfile userProfile) {
        createContactInternal(getEmailForUser(userProfile), userProfile);
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue salary"
    )
    @Transactional
    public void updateSalary(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withSalaryAttributes(userProfile, new Properties()));
    }

    @NotNull
    private UserProfile getUserProfile(UserProfile userProfileIn) {
        return userProfileRepository.findByUserId(userProfileIn.userId())
                .orElseThrow(() -> new GenericTechnicalException("User %s not found - let's retry".formatted(userProfileIn.userId())));
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue global contact info"
    )
    @Transactional
    public void updateContactInfo(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        var attributes = buildLocationAttributes(userProfile);
        withIdentity(getFirstNameForUser(userProfile), getLastNameForUser(userProfile), attributes);
        withSalaryAttributes(userProfile, attributes);
        withSituationAttributes(userProfile, attributes);
        updateContactAttribute(userProfile, attributes);

        // handling possible duplicate phone number error
        updatePhoneNumber(userProfile);
    }

    private Properties withSalaryAttributes(UserProfile userProfile, Properties properties) {
        properties.setProperty(SALARY_SENDINBLUE_PROPERTY, String.valueOf(userProfile.generalInformation().getSalary()));
        return properties;
    }

    private Properties withSituationAttributes(UserProfile userProfile, Properties properties) {
        Optional.ofNullable(userProfile.generalInformation().getSituation())
                .ifPresent(situation ->
                        properties.setProperty(SITUATION_SENDINBLUE_PROPERTY, SITUATION_ENUM_MAPPING.get(situation)));
        return properties;
    }

    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue situation"
    )
    @Transactional
    public void updateSituation(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withSituationAttributes(userProfile, new Properties()));
    }

    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue user mobile usage"
    )
    @Transactional
    public void updateUserMobileUsage(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withUserMobileUsage(userProfile, new Properties()));
    }

    private Properties withUserMobileUsage(UserProfile userProfile, Properties properties) {
        var userMobileUsage = userMobileTokenRepository.findFirstByUserProfileUserIdOrderByTimestampDesc(userProfile.userId()).isPresent();

        var userMobileUsageSibValue = USER_MOBILE_USAGE_ENUM_MAPPING.get(userMobileUsage);

        properties.setProperty(USER_MOBILE_USAGE_SENDINBLUE_PROPERTY, userMobileUsageSibValue);
        return properties;
    }

    private String preprocessSmtpSubject(long templateId) {
        return addMailSubjectPrefixIfRequired(() -> getTemplateSubject(templateId));
    }

    @Nullable
    private String addMailSubjectPrefixIfRequired(Supplier<String> getSubjectToPrefix) {
        return Arrays.stream(environment.getActiveProfiles())
                .map(EnvironmentProfile::fromText)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .map(EnvironmentProfile::getText)
                .map(EnvironmentProfile::formatProfileForEmailSubject)
                .map(envPrefix -> {
                    var subjectToPrefix = getSubjectToPrefix.get();
                    return subjectToPrefix == null ? null : "%s (%s)".formatted(envPrefix, getSubjectToPrefix.get());
                })
                .orElse(null);
    }

    private String getTemplateSubject(long templateId) {
        try {
            var templateInfo = clientConfiguration.getTransactionalEmailApi().getSmtpTemplate(templateId);
            return templateInfo.getSubject();
        } catch (ApiException e) {
            log.error("Unable to retrieve info for template {}, message: {}", templateId, e.getMessage(), e);
            throw new GenericTechnicalException("Unable to retrieve template info", e);
        }
    }


    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue last connection date"
    )
    @Transactional
    public void updateLastConnectionDate(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withLastConnectionDate(userProfile, new Properties()));
    }

    private Properties withLastConnectionDate(UserProfile userProfile, Properties properties) {
        var date = userProfile.lastConnectionDate();
        if (date != null) {
            var formattedDate = sibDateFormat(date);
            properties.setProperty(LAST_CONNECTION_DATE_SENDING_BLUE_PROPERTY, formattedDate);
        }
        return properties;
    }

    @NotNull
    private static String sibDateFormat(TemporalAccessor date) {
        var formatter = DateTimeFormatter.ofPattern(SIB_SIMPLE_DATE_FORMAT);
        return formatter.format(date);
    }

    private Properties withLastCandidatureDate(UserProfile userProfile, Properties properties) {
        getLastCandidatureFromUserProfile(userProfile).ifPresent(candidature -> properties.setProperty(LAST_CANDIDATURE_DATE_SENDINBLUE_PROPERTY, sibDateFormat(candidature.getSubmissionDate())));
        return properties;
    }


    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue last candidature date"
    )
    @Transactional
    public void updateLastCandidatureDate(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withLastCandidatureDate(userProfile, new Properties()));
    }

    private Optional<RecruitmentCandidature> getLastCandidatureFromUserProfile(UserProfile userProfile) {
        return recruitmentCandidatureRepository
                .findTopByUserProfileUserIdAndSubmissionDateIsNotNullOrderBySubmissionDateDesc(userProfile.userId());
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue number of experiences"
    )
    @Transactional
    public void updateNumberOfExperiences(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        updateContactAttribute(userProfile, withNumberOfExperiences(userProfile, new Properties()));
    }

    private Properties withNumberOfExperiences(UserProfile userProfile, Properties properties) {
        properties.setProperty(NUMBER_OF_EXPERIENCES_SENDINBLUE_PROPERTY, String.valueOf(userProfile.experiences().size()));
        return properties;
    }

    private void updateContactAttribute(UserProfile userProfile, Properties attributes) {
        updateContact(getEmailForUser(userProfile), new UpdateContact().attributes(attributes), userProfile);
    }

    private Properties buildCompleteContactAttributes(UserProfile userProfile) {
        var kcUser = keycloakService.getFrontOfficeUserProfile(userProfile.userId());
        var attributes = buildLocationAttributes(userProfile);

        withLastCandidatureDate(userProfile, attributes);
        withLastConnectionDate(userProfile, attributes);
        withNumberOfExperiences(userProfile, attributes);
        withSalaryAttributes(userProfile, attributes);
        withSituationAttributes(userProfile, attributes);
        withUserMobileUsage(userProfile, attributes);
        withIdentity(kcUser.map(UserRepresentation::getFirstName).orElse(""), kcUser.map(UserRepresentation::getLastName).orElse(""), attributes);
        Optional.ofNullable(userProfile.getCreatedDate())
                .map(sendingBlueLegacyDateFormatter::format)
                .ifPresent(d -> attributes.setProperty(CREATION_DATE_SENDINBLUE_PROPERTY, d));
        return attributes;
    }

    private void createContactInternal(String email, UserProfile userProfile, Long... subscribedMailingListIds) {
        try {
            var attributes = buildCompleteContactAttributes(userProfile);
            var contactsApi = clientConfiguration.getContactApi();
            var createContact = new CreateContact()
                    .email(email)
                    .smtpBlacklistSender(new ArrayList<>(userProfile.getSendersOptOut()))
                    .attributes(attributes)
                    .listIds(Lists.asList(defaultNewUserList, subscribedMailingListIds))
                    .updateEnabled(true);

            var createUpdateContactModel = contactsApi.createContact(createContact);
            if (createUpdateContactModel == null) {
                log.debug("Contact with email already exists");
            } else {
                log.debug("Contact with id {} created in sendinblue", createUpdateContactModel.getId());
            }
        } catch (ApiException e) {
            if (e.getCode() == HttpStatus.CONFLICT.value()) {
                log.warn("User {} already exists in SIB", userProfile.userId(), e);
            } else {
                log.error("Failed to create contact - error {} - content: {}", e.getCode(), e.getResponseBody(), e);
                throw new GenericTechnicalException("createContact ApiException", e);
            }
        }
        updatePhoneNumber(userProfile);
    }

    private boolean shouldNotExistInSIB(UserProfile userProfile) {
        return Optional.ofNullable(userProfile)
                .map(UserProfile::isWaitingForBOTermination)
                .orElse(true);
    }


    private String getEmailForUser(UserProfile userProfile) {
        return Optional.ofNullable(getUserRepresentationForUserId(userProfile.userId())).map(UserRepresentation::getEmail).orElse(null);
    }

    @Override
    public boolean getJobDatingOptIn(UserProfile userProfile) {
        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring JD access for {}: user should not exist in SIB", userProfile);
            return false;
        }
        var email = getEmailForUser(userProfile);
        if (email != null) {
            var contactsApi = clientConfiguration.getContactApi();
            try {
                var info = contactsApi.getContactInfo(email, null, null);
                return Optional.ofNullable(info)
                        .map(GetExtendedContactDetails::getListIds)
                        .filter(userLists -> userLists.contains(clientConfiguration.getJobDatingOptinFrontUsersListId()))
                        .isPresent();
            } catch (ApiException apiException) {
                if (apiException.getCode() != HttpStatus.NOT_FOUND.value()) {
                    log.warn("Exception during call to SendInBlue api for email {}", email, apiException);
                } else {
                    log.debug("email {} not found in SendInBlue", email);
                }
                return false;
            }
        }
        return false;
    }

    private String getFirstNameForUser(UserProfile userProfile) {
        return Optional.ofNullable(getUserRepresentationForUserId(userProfile.userId())).map(UserRepresentation::getFirstName).orElse(null);
    }

    private String getLastNameForUser(UserProfile userProfile) {
        return Optional.ofNullable(getUserRepresentationForUserId(userProfile.userId())).map(UserRepresentation::getLastName).orElse(null);
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue location"
    )
    @Transactional
    public void updateLocation(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring location upda    te for {}: user should not exist in SIB", userProfile);
            return;
        }
        var attributes = buildLocationAttributes(userProfile);
        var updateContact = new UpdateContact().attributes(attributes);
        updateContact(getEmailForUser(userProfile), updateContact, userProfile);
    }

    private Properties buildLocationAttributes(UserProfile userProfile) {
        var location = Optional.ofNullable(userProfile).map(UserProfile::generalInformation).map(GeneralInformation::getLocation);
        var attributes = new Properties();
        attributes.setProperty(DEPARTMENT_SENDINBLUE_KEY, Strings.nullToEmpty(location.map(Location::getDepartmentCode).map(StringUtils::trimToNull).orElse(null)));
        attributes.setProperty(REGION_SENDINBLUE_KEY, Strings.nullToEmpty(location.map(Location::getRegionName).map(StringUtils::trimToNull).orElse(null)));
        attributes.setProperty(POSTCODE_SENDINBLUE_KEY, Strings.nullToEmpty(location.map(Location::getPostcode).map(StringUtils::trimToNull).orElse(null)));

        return attributes;
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue phone number"
    )
    @Transactional
    public void updatePhoneNumber(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);

        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring phone number update for {}: user should not exist in SIB", userProfile);
            return;
        }
        if (userProfile.getPhoneNumber() != null) {
            var phoneAttributes = new Properties();
            withPhoneNumber(userProfile, phoneAttributes);
            updateContactAttribute(userProfile, phoneAttributes);
        }
    }


    private void withPhoneNumber(UserProfile userProfile, Properties attributes) {
        attributes.setProperty(PHONE_NUMBER_SENDINBLUE_PROPERTY, Strings.nullToEmpty(userProfile.getPhoneNumber()));
    }


    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue name"
    )
    @Transactional
    public void updateName(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring name update for {}: user should not exist in SIB", userProfile);
            return;
        }
        var attributes = new Properties();
        withIdentity(getFirstNameForUser(userProfile), getLastNameForUser(userProfile), attributes);
        var updateContact = new UpdateContact().attributes(attributes);
        var email = getEmailForUser(userProfile);
        updateContact(email, updateContact, userProfile);
    }

    private static void withIdentity(String firstName, String lastName, Properties attributes) {
        attributes.setProperty(FIRST_NAME_SENDINBLUE_PROPERTY, Strings.nullToEmpty(firstName));
        attributes.setProperty(LAST_NAME_SENDINBLUE_PROPERTY, Strings.nullToEmpty(lastName));
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue job-offer-opt-in"
    )
    @Transactional
    public void updateJobOffersOptIn(UserProfile userProfileIn) {
        var userProfile = getUserProfile(userProfileIn);
        var jobOfferOptIn = BooleanUtils.negate(userProfile.getJobOfferOptOut());
        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring job offer optin update for {}: user should not exist in SIB", userProfile);
            return;
        }
        if (jobOfferOptIn != null) {
            addOrRemoveBlacklistedTransactionalSender(getEmailForUser(userProfile),
                    !jobOfferOptIn,
                    userProfile
            );
        } else {
            log.debug("Ignore job offer option - null value - for {}", userProfile);
        }
    }


    private void addOrRemoveBlacklistedTransactionalSender(String email, boolean optOutJob, UserProfile userProfile) {
        try {
            if (email != null) {
                if (optOutJob) {
                    userProfile.addSendersOptOut(jobOfferSender);
                    clientConfiguration.getContactApi().updateContact(email, new UpdateContact().smtpBlacklistSender(List.of(jobOfferSender)));
                } else {
                    userProfile.removeSenderOptOut(jobOfferSender);
                    clientConfiguration.getTransactionalEmailApi().smtpBlockedContactsEmailDelete(email);
                    if (!userProfile.getSendersOptOut().isEmpty()) {
                        clientConfiguration.getContactApi().updateContact(email, new UpdateContact().smtpBlacklistSender(new ArrayList<>(userProfile.getSendersOptOut())));
                    }
                }
            }
        } catch (ApiException apiException) {
            if (apiException.getCode() == HttpStatus.NOT_FOUND.value()) {
                log.debug("email {} not found in SendInBlue ; ignoring transactional mail configuration sending", email);
            } else {
                log.warn("Exception during call to SendInBlue api for email {} - response {}", email, apiException.getResponseBody(), apiException);
                throw new GenericTechnicalException("updateJobOffersOptIn ApiException", apiException);
            }
        }
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Update send in blue job-dating-opt-in"
    )
    @Transactional
    public void updateNewsOptIn(UserProfile userProfileIn, Boolean newsOptIn) {
        var userProfile = getUserProfile(userProfileIn);

        if (shouldNotExistInSIB(userProfile)) {
            log.info("Ignoring job dating optin update for {}: user should not exist in SIB", userProfile);
            return;
        }
        var email = getEmailForUser(userProfile);
        if (email != null) {
            if (BooleanUtils.isTrue(newsOptIn)) {
                addContactToLists(email, userProfile, clientConfiguration.getJobDatingOptinFrontUsersListId());
            } else {
                removeContactFromLists(email, userProfile, clientConfiguration.getJobDatingOptinFrontUsersListId());
            }
        }
    }

    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Send mail for template"
    )
    @Override
    public CompletableFuture<Set<String>> sendMailsToProfilesForTemplate(
            Collection<UserProfile> userProfiles,
            String notificationLabel,
            long templateId,
            @Nullable Object globalParams,
            @Nullable Map<String, Map<String, Object>> perUserIdParamIn,
            String... forcedEmail) {
        try {
            var templateSender = getTemplateSender(templateId);
            var profilesPerBlacklistedStatus = userProfiles.stream().collect(Collectors.groupingBy(p -> p.getSendersOptOut().contains(templateSender)));
            var optInUserProfiles = profilesPerBlacklistedStatus.getOrDefault(false, new ArrayList<>());
            var userProfileForUserRepresentation = buildUserProfileForUserRepresentationMap(optInUserProfiles);

            var ignoredUserIds = profilesPerBlacklistedStatus.getOrDefault(true, new ArrayList<>());
            if (!ignoredUserIds.isEmpty()) {
                log.debug("Ignoring users {} when sending template {} due to blacklisted sender", ignoredUserIds.stream().map(UserProfile::userId).collect(Collectors.joining(", ")), templateId);
            }
            Map<String, Map<String, Object>> perUserIdParam = null;
            if (perUserIdParamIn != null) {
                perUserIdParam = new HashMap<>(perUserIdParamIn);
                ignoredUserIds.stream().map(UserProfile::userId).forEach(perUserIdParam::remove);
            }
            var emailsSent = sendMailUnsafeInternal(
                    userProfileForUserRepresentation.keySet().stream().map(UserRepresentation::getEmail).collect(Collectors.toSet()),
                    templateId,
                    globalParams,
                    buildPerEmailMap(perUserIdParam, userProfileForUserRepresentation.keySet()),
                    null, forcedEmail
            )
                    .stream()
                    .map(email -> userProfileForUserRepresentation.keySet().stream().filter(e -> e.getEmail().equals(email)).map(UserRepresentation::getId).findFirst().orElse(""))
                    .collect(Collectors.toSet());
            if (!Strings.isNullOrEmpty(notificationLabel)) {
                var message = EmailSendingNotifierMessageDTO.builderForSuccess()
                        .notificationLabel(notificationLabel)
                        .successfulSentEmailsLength(emailsSent.size())
                        .build();
                notifier.sendMessage(message);
            }
            return CompletableFuture.completedFuture(emailsSent);
        } catch (ApiException e) {
            var status = HttpStatus.resolve(e.getCode());
            if (status != null && status.is4xxClientError()) {
                if (status == HttpStatus.NOT_FOUND) {
                    log.error("Template not found for id {} ", templateId);
                } else {
                    log.error("Unable to send mail to {} users for template {} (status: {}, body: {}) - no retry", userProfiles.size(), templateId, e.getCode(), e.getResponseBody(), e);
                }
                if (!Strings.isNullOrEmpty(notificationLabel)) {
                    var message = EmailSendingNotifierMessageDTO.builderForFailure()
                            .notificationLabel(notificationLabel)
                            .build();
                    notifier.sendMessage(message);
                }
            } else {
                log.warn("Got error when trying to send mail template {} to {} profiles, status = {}, body = {}, - let's retry", templateId, userProfiles.size(), status, e.getResponseBody(), e);
                throw new GenericTechnicalException("unable to send mail for template " + templateId, e);
            }
        }
        return CompletableFuture.completedFuture(Collections.emptySet());
    }

    private @NotNull Map<String, Map<String, Object>> buildPerEmailMap(Map<String, Map<String, Object>> perUserIdParamMap, Set<UserRepresentation> userRepresentations) {
        var perEmailParamMap = new HashMap<String, Map<String, Object>>();
        if (perUserIdParamMap != null) {
            perUserIdParamMap.forEach((key, value) -> userRepresentations.stream().filter(u -> u.getId().equals(key))
                    .findFirst()
                    .ifPresentOrElse(u -> perEmailParamMap.put(u.getEmail(), new HashMap<>(value)), () -> log.error("No user with userId {} found", key)));
        }
        userRepresentations.stream()
                .filter(u -> u.getFirstNameOrFullName() != null)
                .forEach(u -> {
                    Map<String, Object> firstNameMap = Map.of(FIRST_NAME_SENDINBLUE_PROPERTY, u.getFirstNameOrFullName());
                    Optional.ofNullable(perEmailParamMap.get(u.getEmail())).ifPresentOrElse(
                            m -> m.putAll(firstNameMap),
                            () -> perEmailParamMap.put(u.getEmail(), firstNameMap)
                    );
                });
        return perEmailParamMap;
    }

    private String getTemplateSender(long templateId) throws ApiException {
        var sender = clientConfiguration.getTransactionalEmailApi().getSmtpTemplate(templateId).getSender().getEmail();
        log.debug("Got sender {} for template {}", sender, templateId);
        return sender;
    }

    @NotNull
    private Map<UserRepresentation, UserProfile> buildUserProfileForUserRepresentationMap(List<UserProfile> optInUserProfiles) {
        return optInUserProfiles.stream()
                .map(u -> new AbstractMap.SimpleEntry<>(u, getUserRepresentationForUserId(u.userId())))
                .filter(e -> e.getValue() != null)
                .collect(Collectors.toMap(AbstractMap.SimpleEntry::getValue, AbstractMap.SimpleEntry::getKey));
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Send mail for template"
    )
    public CompletableFuture<Collection<String>> sendMailsForTemplate(Set<String> allEmails, long templateId, Object params, FilePartProvider filePartProvider, String... forcedEmails) {
        try {
            var sender = getTemplateSender(templateId);
            var emails = allEmails.stream().filter(email -> !isSenderBlacklistedForEmail(email, sender)).collect(Collectors.toSet());
            var mailsSent = sendMailUnsafeInternal(emails, templateId, params, null, filePartProvider, forcedEmails);
            return CompletableFuture.completedFuture(mailsSent);
        } catch (ApiException e) {
            log.warn("Unable to send mail - delegate to retry handler: body={}", e.getResponseBody(), e);
            throw new GenericTechnicalException("sendTransacEmail ApiException", e);
        }
    }

    private Collection<String> sendMailUnsafeInternal(
            Set<String> emails,
            long templateId,
            Object globalParams,
            Map<String, Map<String, Object>> perEmailParams,
            FilePartProvider filePartProvider,
            String... forcedEmails) throws ApiException {
        var subject = preprocessSmtpSubject(templateId);
        var finalEmails = emails.stream()
                // Ensure email is 'well formed' to avoid the whole request being ignored
                .filter(com.erhgo.utils.StringUtils::isEmail)
                .filter(emailToNotifyFilter::emailAccepted)
                .sorted()
                .collect(Collectors.toCollection(ArrayList::new));
        if (!finalEmails.isEmpty()) {
            finalEmails.addAll(List.of(forcedEmails));
            var emailsSent = new ArrayList<String>();
            var splitEmailsForOneShot = splitEmailsIntoMaxRequestSize(finalEmails);
            log.debug("About to send {} emails in {} requests with {} forcedEmails", finalEmails.size(), splitEmailsForOneShot.size(), forcedEmails.length);
            var attachment = Optional.ofNullable(filePartProvider).map(this::createAttachment);
            for (var emailsForOneShot : splitEmailsForOneShot) {
                emailsSent.addAll(sendOneShot(templateId, globalParams, emailsForOneShot, perEmailParams, attachment, subject));
            }
            return emailsSent;
        }
        log.info("All emails where ignored in {} - no mail sent", emails);
        return finalEmails;
    }

    private SendSmtpEmailAttachment createAttachment(@NotNull FilePartProvider filePartProvider) {
        try {
            return new SendSmtpEmailAttachment()
                    .content(filePartProvider.readAllBytes())
                    .name(filePartProvider.fileName())
                    ;
        } catch (IOException e) {
            log.error("Unable to create attachment named {} - mails are NOT sent", filePartProvider.fileName(), e);
            throw new GenericTechnicalException("Unable to create attachment named " + filePartProvider.fileName(), e);
        }
    }

    private boolean isSenderBlacklistedForEmail(String userEmail, String senderEmail) {
        boolean isSenderBlacklisted = getUserProfileFromEmail(userEmail).map(UserProfile::getSendersOptOut).map(c -> c.contains(senderEmail)).orElse(false);
        if (isSenderBlacklisted) {
            log.debug("Sender {} blacklisted for user {}", senderEmail, userEmail);
        }
        return isSenderBlacklisted;
    }

    private Optional<UserProfile> getUserProfileFromEmail(String email) {
        return Optional.ofNullable(keycloakService.getFOUserRepresentationByEmail(email)).map(UserRepresentation::getId).flatMap(userProfileRepository::findByUserId);
    }

    private List<List<String>> splitEmailsIntoMaxRequestSize(ArrayList<String> emails) {
        return Lists.partition(emails, maxRecipientsPerRequest);
    }

    private List<String> sendOneShot(long templateId, Object params, List<String> smtpEmailsBcc, Map<String, Map<String, Object>> perEmailParams, Optional<SendSmtpEmailAttachment> attachment, String subject) throws ApiException {
        var sendSmtpEmail = new SendSmtpEmail()
                .subject(subject)
                .templateId(templateId);
        // next line: see ERHGO-1494 and https://developers.sendinblue.com/reference/sendtransacemail > messageVersions doc
        var messageVersions = splitEmailsIntoSmallEnoughLists(smtpEmailsBcc, perEmailParams, params, subject);
        sendSmtpEmail
                .messageVersions(messageVersions)
        ;
        attachment.ifPresent(sendSmtpEmail::addAttachmentItem);
        var sendEmail = sendTransactionalEmail(sendSmtpEmail);
        log.debug("Email sent with id {} to {} users", sendEmail.getMessageIds(), smtpEmailsBcc.size());
        return smtpEmailsBcc;
    }

    @NotNull
    private List<SendSmtpEmailMessageVersions> splitEmailsIntoSmallEnoughLists(List<String> smtpEmailsBcc, Map<String, Map<String, Object>> perEmailParams, Object params, String subject) {
        return smtpEmailsBcc.stream()
                .map(email -> new SendSmtpEmailMessageVersions()
                        .subject(subject)
                        .to(List.of(new SendSmtpEmailTo1().email(email)))
                        .params(unionMap(Optional.ofNullable(perEmailParams).map(m -> m.get(email)).orElse(null), params))
                )
                .toList();
    }

    private Map<String, Object> unionMap(Map<String, Object> parEmailParam, Object params) {
        var unionMap = new HashMap<String, Object>();
        if (parEmailParam != null) unionMap.putAll(parEmailParam);
        if (params instanceof Map globalParam) unionMap.putAll(globalParam);
        return unionMap;
    }

    @Override
    @Async
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Send single mail"
    )
    public CompletableFuture<Boolean> sendMail(
            String recipientEmail,
            String recipientName,
            String subject,
            String content,
            String emailFrom,
            String authorAlias
    ) {
        if (!emailToNotifyFilter.emailAccepted(recipientEmail)) {
            log.info("Ignored recipient - No email sent");
            return CompletableFuture.completedFuture(false);
        }
        try {
            var sendSmtpEmailTo = new SendSmtpEmailTo()
                    .email(recipientEmail)
                    .name(StringUtils.trimToNull(recipientName));

            var sendSmtpEmailSender = new SendSmtpEmailSender().email(emailFrom).name(StringUtils.trimToNull(authorAlias));

            var sendSmtpEmail = new SendSmtpEmail()
                    .subject(addMailSubjectPrefixIfRequired(() -> subject))
                    .textContent(content)
                    .sender(sendSmtpEmailSender)
                    .to(Collections.singletonList(sendSmtpEmailTo));

            var sendEmail = sendTransactionalEmail(sendSmtpEmail);
            log.debug("Email sent id {}", sendEmail.getMessageId());
            return CompletableFuture.completedFuture(true);
        } catch (ApiException e) {
            log.warn("Unable to send mail - delegate to retry handler - content: {}", e.getResponseBody(), e);
            throw new GenericTechnicalException("sendMail ApiException", e);
        }
    }

    private CreateSmtpEmail sendTransactionalEmail(SendSmtpEmail sendSmtpEmail) throws ApiException {
        return clientConfiguration.getTransactionalEmailApi().sendTransacEmail(sendSmtpEmail);
    }

    @Override
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Get transactional blacklisted emails"
    )
    public List<TransactionalBlackListResult> getTransactionalBlacklistedEmails(Long limit, Long offset, LocalDateTime modifiedSince) {
        List<String> allSenders = new ArrayList<>();
        try {
            clientConfiguration.getSendersApi().getSenders(null, null).getSenders().stream().map(GetSendersListSenders::getEmail).forEach(allSenders::add);
        } catch (ApiException e) {
            log.error("Unable to retrieve tx senders, response: {}, status: {}", e.getResponseBody(), e.getCode(), e);
            throw new GenericTechnicalException("Unable to retrieve tx senders", e);
        }
        try {
            var transactionalEmailsApi = clientConfiguration.getTransactionalEmailApi();
            return transactionalEmailsApi
                    .getTransacBlockedContacts(modifiedSince == null ? null : sendingBlueLocaleDateTimeFormatter.format(modifiedSince), modifiedSince == null ? null : sendingBlueLocaleDateTimeFormatter.format(LocalDateTime.now()), limit, offset, null, null)
                    .getContacts()
                    .stream()
                    .collect(Collectors.groupingBy(GetTransacBlockedContactsContacts::getEmail))
                    .entrySet()
                    .stream()
                    .map(e -> {
                        // When all tx users are blocked, sib seams to serialize only one null value ¯\_(ツ)_/¯
                        var blockedSenders = e.getValue().stream().anyMatch(a -> a.getSenderEmail() == null
                        ) ? allSenders :
                                e.getValue()
                                        .stream()
                                        .map(GetTransacBlockedContactsContacts::getSenderEmail)
                                        .toList();
                        return new TransactionalBlackListResult(e.getKey(), blockedSenders);
                    })
                    .toList();
        } catch (ApiException e) {
            log.error("Failed to get transactional blacklisted contacts from SendInBlue, status: {}, response: {}", e.getCode(), e.getResponseBody(), e);
            throw new GenericTechnicalException("Failed to get transactional blacklisted contacts from SendInBlue - page: " + offset + ", limit: " + limit + ", senders: " + String.join(", ", allSenders), e);
        }
    }


    private final ObjectMapper objectMapper;

    @Override
    public List<MailingUserDTO> getContacts(Long limit, Long offset, LocalDateTime modifiedSince) {
        var contactsApi = clientConfiguration.getContactApi();
        try {
            return contactsApi.getContacts(limit, offset, modifiedSince == null ? null : DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(modifiedSince), null)
                    .getContacts()
                    .stream()
                    .map(c -> objectMapper.convertValue(c, MailingUserDTO.class))
                    .toList();
        } catch (ApiException e) {
            log.error("Failed to get SIB contacts, status: {}, response: {}, message: {}", e.getCode(), e.getResponseBody(), e.getMessage(), e);
            throw new GenericTechnicalException(FAILED_TO_GET_SENDINBLUE_CONTACTS, e);
        }
    }


    @Override
    public List<MailingUserDTO> getListUsersJobDating(Long limit, Long offset) {
        var contactsApi = clientConfiguration.getContactApi();
        try {
            return contactsApi.getContactsFromList(clientConfiguration.getJobDatingOptinFrontUsersListId(), null, limit, offset, null)
                    .getContacts()
                    .stream()
                    .map(c -> objectMapper.convertValue(c, MailingUserDTO.class))
                    .toList();
        } catch (ApiException e) {
            log.error("Failed to lis users job dating, status: {}, response: {}, message: {}", e.getCode(), e.getResponseBody(), e.getMessage(), e);
            throw new GenericTechnicalException(FAILED_TO_GET_SENDINBLUE_CONTACTS, e);
        }
    }

    private void deleteEmail(String email) {
        try {
            clientConfiguration.getContactApi().deleteContact(email);
        } catch (ApiException e) {
            if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
                log.debug("Contact was not found in SendInBlue - ignore deletion");
            } else {
                throw new GenericTechnicalException("Failed to remove contact %s from SendInBlue (body: %s)", e);
            }
        }
    }

    @Override
    public void processFOEmailUpdate(String previousEmail, String newEmail) {
        if (checkIfContactExistsInSIB(newEmail)) {
            refreshFOContactWithNewEmail(previousEmail, newEmail);
        } else {
            updateFOEmailSafely(previousEmail, newEmail);
        }
    }


    private void updateFOEmailSafely(String previousEmail, String newEmail) {
        try {
            var attributes = new Properties();
            attributes.setProperty(EMAIL_SENDINBLUE_PROPERTY, newEmail);
            var updateContact = new UpdateContact().attributes(attributes);
            clientConfiguration.getContactApi().updateContact(previousEmail, updateContact);
        } catch (ApiException apiException) {
            if (apiException.getCode() == HttpStatus.NOT_FOUND.value()) {
                log.info("The user {} doesn't exist - let's create it with {}", previousEmail, newEmail);
                createContact(getUserProfileFromEmail(newEmail).orElseThrow());
            } else {
                log.error("Failed to update contact email from {} to {} - status: {}", previousEmail, newEmail, apiException.getCode(), apiException);
            }
        }
    }

    private void refreshFOContactWithNewEmail(String previousEmail, String newEmail) {
        deleteEmail(previousEmail);
        log.info("Old contact with email {} has been deleted", previousEmail);

        var userProfile = getUserProfileFromEmail(newEmail).orElseThrow();
        var attributes = buildCompleteContactAttributes(userProfile);
        updateContactAttribute(userProfile, attributes);
        log.info("New contact {} has been updated with user {} details", newEmail, userProfile.userId());

        var phoneAttributes = new Properties();
        withPhoneNumber(userProfile, phoneAttributes);
        updateContactAttribute(userProfile, phoneAttributes);
        log.info("Phone number {} has been updated for user {}", userProfile.getPhoneNumber(), userProfile.userId());
    }

    private boolean checkIfContactExistsInSIB(String email) {
        try {
            clientConfiguration.getContactApi().getContactInfo(email, null, null);
            log.debug("Contact with email {} exists in SendInBlue", email);
            return true;
        } catch (ApiException e) {
            if (e.getCode() == HttpStatus.NOT_FOUND.value()) {
                log.debug("Contact with email {} doesn't exist in SendInBlue", email);
                return false;
            }
            log.warn("Unable to get contact details: body={}", e.getResponseBody(), e);
            throw new GenericTechnicalException("getSenders ApiException", e);
        }
    }

    private void addContactToLists(String email, UserProfile userProfile, Long listIds) {
        var updateContact = new UpdateContact().listIds(List.of(listIds));
        updateContact(email, updateContact, userProfile);
    }

    private void removeContactFromLists(String email, UserProfile userProfile, Long... listIds) {
        var updateContact = new UpdateContact()
                .unlinkListIds(List.of(listIds));
        updateContact(email, updateContact, userProfile);
        log.debug("Contact {} removed from lists but not removed from sendinblue", email);
    }

    private void updateContact(String email, UpdateContact updateContact, UserProfile userProfile) {
        try {
            if (email != null) {
                clientConfiguration.getContactApi().updateContact(email, updateContact);
            }
        } catch (ApiException apiException) {
            handleSIBException(email, updateContact, userProfile, apiException);
        }
    }

    private void handleSIBException(String email, UpdateContact updateContact, UserProfile userProfile, ApiException apiException) {
        var body = apiException.getResponseBody();
        if (apiException.getCode() == HttpStatus.NOT_FOUND.value()) {
            log.debug("The user {} doesn't exist", email, apiException);
            var subscribedMailingListIds = updateContact.getListIds() != null ? updateContact.getListIds().toArray(new Long[0]) : new Long[]{};
            createContactInternal(email, userProfile, subscribedMailingListIds);
            return;
        } else if (apiException.getCode() == HttpStatus.BAD_REQUEST.value()) {
            handleSIBBadRequest(email, updateContact, apiException, body);
            return;
        } else {
            log.warn("Unable to update contact, given attributes: {}, cause : {}", updateContact.getAttributes(), body, apiException);
        }
        // exception is expected by the Retry annotation
        throw new GenericTechnicalException("Unable to update contact", apiException);
    }


    private static void handleSIBBadRequest(String email, UpdateContact updateContact, ApiException apiException, String body) {
        if (isErrorDueToPhoneAlreadyUsed(body)) {
            log.warn("Unable to update contact in SendInBlue for {} - bad request ; probably due to existant user with same phone (given attributes: {})? - error  {}", email, updateContact.getAttributes(), body, apiException);
        } else if (isErrorDueToInvalidPhone(body)) {
            log.warn("Unable to update contact in SendInBlue for {} - bad request ; probably due to invalid phone number (given attributes: {})? - error  {}", email, updateContact.getAttributes(), body, apiException);
        } else {
            log.error("Unknown error for user {}, given attributes: {}, status: {}, cause {}", email, updateContact.getAttributes(), body, apiException.getCode(), apiException);
        }
    }

    private static boolean isErrorDueToInvalidPhone(String body) {
        return body != null && body.toLowerCase().contains("invalid") && body.toLowerCase().contains("phone");
    }

    private static boolean isErrorDueToPhoneAlreadyUsed(String body) {
        return body != null &&
               // FIXME: remove 'email' check part when https://help.brevo.com/hc/fr/requests/3592311 is solved
               (body.toLowerCase().contains("sms") || body.toLowerCase().contains("email"))
               && body.toLowerCase().contains("already");
    }

    public UserRepresentation getUserRepresentationForUserId(String userId) {
        return keycloakService.getFrontOfficeUserProfile(userId)
                .orElseGet(() -> {
                    log.warn("No email found for {}", userId);
                    return null;
                });
    }

    @Override
    @Retryable(
            retryFor = GenericTechnicalException.class,
            maxAttempts = 10,
            // Try during around two hours with increasing delays
            backoff = @Backoff(delayExpression = "${sendinblue.retry-delay:5000}", multiplier = 1.8),
            label = "Block deleted user email from all transactional senders"
    )
    public void blacklistFromAllSenders(UserProfile userProfile) {
        try {
            var getSendersList = clientConfiguration.getSendersApi().getSenders(null, null);
            var email = getEmailForUser(userProfile);
            var senders = getSendersList
                    .getSenders().stream().map(GetSendersListSenders::getEmail).toList();
            updateContact(email, new UpdateContact().smtpBlacklistSender(senders), userProfile);
            log.debug("Sender {} blacklisted successfully for user {}", senders, userProfile.userId());
        } catch (ApiException e) {
            log.warn("Unable to get transactional senders: body={}", e.getResponseBody(), e);
            throw new GenericTechnicalException("getSenders ApiException", e);
        }
    }
}
