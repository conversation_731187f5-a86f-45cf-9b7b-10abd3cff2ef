package com.erhgo.services.mailing;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.userprofile.FilePartProvider;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@ConditionalOnExpression("'${sendinblue.apiKey}'.isEmpty()")
public class MockMailingListService implements MailingListService {

    public static final String IGNORE_OPERATION_WARNING = "Ignoring operation due to lack of SendInBlue configuration";
    public static final String RETURN_FALSE_OPERATION_WARNING = "Returning false due to lack of SendInBlue configuration";

    @PostConstruct
    public void initialize() {
        log.error("WARNING - mailing service is mocked");
    }

    @Override
    public void updateLastConnectionDate(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateLastCandidatureDate(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateNumberOfExperiences(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @SuppressWarnings("java:S4144")
    @Override
    public boolean getJobDatingOptIn(UserProfile userProfile) {
        log.warn(RETURN_FALSE_OPERATION_WARNING);
        return false;
    }

    @SuppressWarnings("java:S4144")
    @Override
    public List<MailingUserDTO> getListUsersJobDating(Long limit, Long offset) {
        log.warn(IGNORE_OPERATION_WARNING);
        return Collections.emptyList();
    }

    @Override
    public void blacklistFromAllSenders(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateLocation(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updatePhoneNumber(UserProfile userProfile) {
        log.warn("Returning false due to lack of SendinBlue's configuration");
    }

    @Override
    public void updateName(UserProfile userProfile) {
        log.warn("Returning false due to lack of SendinBlue's configuration");
        // No op.
    }


    @Override
    public void updateJobOffersOptIn(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateNewsOptIn(UserProfile userProfile, Boolean optIn) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateSalary(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateSituation(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateContactInfo(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    public void updateUserMobileUsage(UserProfile userProfile) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }

    @Override
    @Async
    public CompletableFuture<Set<String>> sendMailsToProfilesForTemplate(Collection<UserProfile> userProfiles, String notificationLabel, long templateId, Object globalParams, Map<String, Map<String, Object>> perUserIdParam, String... forcedEmail) {
        log.warn(IGNORE_OPERATION_WARNING);
        return CompletableFuture.completedFuture(null);
        // No op.
    }

    @Override
    public CompletableFuture<Collection<String>> sendMailsForTemplate(Set<String> email, long templateId, Object params, FilePartProvider filePartProvider, String... forcedEmails) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
        return CompletableFuture.completedFuture(new ArrayList<>());
    }

    @Override
    public CompletableFuture<Boolean> sendMail(String email, String recipientName, String subject, String content, String emailFrom, String authorAlias) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public List<TransactionalBlackListResult> getTransactionalBlacklistedEmails(Long limit, Long offset, LocalDateTime modifiedSince) {
        log.warn(IGNORE_OPERATION_WARNING);
        return new ArrayList<>();
    }

    @Override
    public List<MailingUserDTO> getContacts(Long limit, Long offset, LocalDateTime modifiedSince) {
        log.warn("Returning empty list due to lack of SendInBlue configuration");
        return Collections.emptyList();
    }

    @Override
    public void processFOEmailUpdate(String previousEmail, String newEmail) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }


}
